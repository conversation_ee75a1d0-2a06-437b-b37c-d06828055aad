{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "a2523ee572d34a1df6925e1e93a0e1f30fbb48d4066983158b925402d13fcc1d"}], "containerPath": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator18.4", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Products", "derivedDataPath": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/build", "indexDataStoreFolderPath": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Debug", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPhone15,4", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "18.4", "ASSETCATALOG_FILTER_FOR_THINNING_DEVICE_CONFIGURATION": "iPhone15,4", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "COLOR_DIAGNOSTICS": "YES", "diagnostic_message_length": "80", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "ONLY_ACTIVE_ARCH": "YES", "TARGET_DEVICE_IDENTIFIER": "5E681F18-63F8-41A7-A94B-7CF1277BD22E", "TARGET_DEVICE_MODEL": "iPhone15,4", "TARGET_DEVICE_OS_VERSION": "18.4", "TARGET_DEVICE_PLATFORM_NAME": "iphonesimulator"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}