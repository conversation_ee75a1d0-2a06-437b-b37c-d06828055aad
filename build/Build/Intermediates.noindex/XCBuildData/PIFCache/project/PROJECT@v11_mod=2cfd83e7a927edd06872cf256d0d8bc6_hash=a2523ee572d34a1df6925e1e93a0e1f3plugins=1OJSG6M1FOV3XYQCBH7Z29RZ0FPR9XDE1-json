{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.4", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "a2523ee572d34a1df6925e1e93a0e1f39ad37cdb5f8b2c679e9aafcf756094fd", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.4", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "a2523ee572d34a1df6925e1e93a0e1f3de90b14f1769473594a6179eb0d393b1", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3fc86886d9f64e48e8639eb53f6bba71a", "path": "Color+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3c66162dd6ac606d6b0fde1125c0cbcad", "path": "String+Localization.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3792095a32650c6510b148a48261a6898", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3debb76cd0cfeb56928bd09876e336910", "path": "爸爸头像.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3af7d712362b2950f307bfeb215da2bc1", "path": "宝箱未打开.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f36015c1296be30529d70cd2006f5211b1", "path": "宝箱已打开.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f30b2213a416541e52fd3f0fadb7114996", "path": "初级会员.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a80371199c1234557727ae66758a003d", "path": "道具配置.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3609e03d58643e6a2dd2cda4bb52bdd72", "path": "登录页面logo.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3b81db7d268d6b5d8a8ae70d5588fb72b", "path": "高级会员.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3100689f00ba39e1c0191fefebb25f5fa", "path": "个人中心插图.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3be8da3f0af9c173e4231de0ccd4ca569", "path": "刮刮卡.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f32dc370040208d41f34ea7af11226c9ea", "path": "刮刮卡中奖.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f31d8124d29e75af253cfbd3936f803fa8", "path": "关闭.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f38a019c2865040717826505930867b5c3", "path": "皇冠.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3f8b4b35a623aeb3d4b017480575ef621", "path": "皇冠(订阅）.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f312095a6414de66cf550034122ba522df", "path": "进入.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3c863bfa26f38a362353a431350b66cf8", "path": "录音.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f36c14dfd1f74a05d5f891fc3025c3296c", "path": "妈妈头像.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e498f57835b7407d076d4df383915a95", "path": "男生头像.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3aa2546ce3ef84ca61a511c237c56203f", "path": "女生头像.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f35fcd08bca0fe6aa3dcb52ec9dd6f04ae", "path": "其他头像.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e4d6643aadf09bf07e23f5e97dd70950", "path": "全班操作.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f35411286f479fad96b03249e668e7cdb7", "path": "添加学生.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.jpeg", "guid": "a2523ee572d34a1df6925e1e93a0e1f3cb2f0774232c250d4a7b13028df8c4f5", "path": "团团转logo.jpg", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f34be0e30c67d4ee2fbc5406433640f010", "path": "烟花.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f30fdd01c21e0a0d7eee67fcf58b5563b9", "path": "banjixinxi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3ad874d76f81ae00a3e3dada83254e88b", "path": "chengzhang.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f30751a36ea5854e2d68fecf20e6308cf2", "path": "choujiang.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3788858ecb9c67b7cebd92e0f24b7cc9a", "path": "denglu.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3b61f782bd0106f6bb6a56c4bd3cbbf6e", "path": "fanhui.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e461f41b6d44306f7f361a6893fdf110", "path": "fenxi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3c1b729ad9d4ad861562d25ffadefd369", "path": "guanyu.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3b57867f3a34fcbd47c51cad27668d4d7", "path": "guizepeizhi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f36534941bf43c475cc171dac4502f8a23", "path": "guzhangfankui.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f35478d896ab9c6d963dd7c497174bd78a", "path": "huiyuan.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f317d762af0ba464e18f43f12aa0c23cc8", "path": "jianpan.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d0ba959ffcb360b0d021ed32d1eefa91", "path": "laoshi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e0058ace2a73899a9f91b553b88402f6", "path": "lingqujilu.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3c3d637e08dec9f392394230cba70a0e5", "path": "lishi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f30b39ecb966a4987ed4bfdaf0e19ccec3", "path": "logo.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3441f868998aa599cfc3f76df0bba6ac5", "path": "shanchu.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3c2b0a56ba1242a0941eaed328d093c54", "path": "shouye1_1.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3170ac5f4655830950cd418015817ea09", "path": "sousuo.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d211840395ca2370b21e2c39397c0b5b", "path": "tuichu.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f30c5e78d62f622ee292c35b9b78e41d3e", "path": "wode13.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f327cd33a7193bfddb2f717014ccc3fc5b", "path": "yinsizhengce.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3f0a8f2d2eedf7c95bdc0ee6dd2b6d208", "path": "yonghuxieyi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f36f9c1a44a18bdb1a4b2255bc9006320d", "path": "yuyan.png", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f33c44a688130331ec949af3c8fc3a4307", "name": "image", "path": "image", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f329e1ccec02ccbc14b2342fbb9bfbcf77", "path": "CoreDataExtensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f34aaaa104f7431d6b73537a9759493fd8", "path": "DataManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3b2632b1643c69c6010eee0dcc0703fcb", "path": "DataModelUsageExamples.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a4d956c762a817279fcff5e935ee4246", "path": "DateRangeType.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f39a5fdd3dd80076215bf570f5808208de", "path": "MemberPointsModels.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3051b99ebe18369b436e4ce8aebfc594c", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d4244747d2d8d8d5550f88f77fba00b3", "path": "DesignSystem.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f317f3a318e7b12295ede016e62e2611bf", "path": "ResponsiveLayoutConfig.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f37389fc421597e22bfad8232615440082", "name": "Styles", "path": "Styles", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f37efe7d57019fb19b9c7cd4171978259f", "path": "GrowthDiaryViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f35306e8fd3bd424db4036b5b13a4c8fc5", "path": "HomeViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f30db24b5341d772973051c18b488d4c25", "path": "LotteryToolFormData.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f395364fa1009263bacbcf8adf3b5e4fbc", "path": "MemberDetailViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f359cd62fd98e3541928578f270925f3dc", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f33c70cb868973c6efcb1b656faf6ee68a", "path": "ActionButtonsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a1d69a077b56b9cf5dde13d713b56b99", "path": "AddMemberFormView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3c250d9824da5402df4ea9b79ce3d3366", "path": "AddRewardFormView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f379b2924f52aebe9eb06482bc5d8d8c37", "path": "CustomRewardExchangeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f353b5345aa7fe428febcf3f09bcd4313e", "path": "CustomTabBar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3488dce8fe3f8b084447ca7584bee58cc", "path": "DatePickerPopupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f30fd0f1f0850bc1c476e02a42529014b7", "path": "DatePickerView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f36e907666faec0ce58071d0a1dc0f4ff8", "path": "DateRangePickerView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f36b20286ea33665584450357efe372db4", "path": "FamilyMemberCardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f336636734ddb808c2a0e706129c16ec34", "path": "FamilyMemberGridView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3f3cdbbe198a2447539aa67607eedb7a3", "path": "FamilyOperationFormView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f33daac4fc5ad2cfc03470da106ddfcad4", "path": "FamilyOperationOptionsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f37b3d689bca00101ac34bd3da3c6a1748", "path": "FamilyTotalScoreView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f313b2505d6a88ae72a8627bb165867be6", "path": "LiquidTabBarBackground.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f34d965ec9a82d25690bf941b3115ec8af", "path": "LiquidTabBarBubble.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f395688a89abc44902738cbd6ed7cc51d4", "path": "LiquidTabBarShape.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3b679b33f88594a1befb1b47676c33560", "path": "LotteryConfigMainView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f38e03b22f6a2f454d96332f212ceae4b5", "path": "LotteryToolConfigFormView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a3cc6942fdf89bbc8218b3a15589d5fa", "path": "LotteryToolItemConfigCard.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3f7d9294d80d2147a472342b3bd968170", "path": "LotteryToolSelectionPopupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3db26a3eb2b19a30d504280aa59d2c874", "path": "MemberPickerPopupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3558e84b2a71a2ac71f1df7e2646f5f65", "path": "MemberPointsFormView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f35182bb81db3025f22a5296f9ef831c71", "path": "MemberPointsOptionsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f36fd4fe9df54a7a738ccb409eb40361cf", "path": "MemberRewardExchangeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3deba2b0b774de13b37662104d0bac151", "path": "MemberSelectionPopupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e1c049f81acab9041dc078cbe9b4eef9", "path": "RoleSelectionView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f33b71ba59d66c4e0ef7de901144f78dfc", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f33d13adf35c384f68fb547376851d808e", "path": "CardComparisonTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f38d9d9770141494e1939175a018f6dc9a", "path": "GrowthDiaryView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3b43e6f2792eeac5007bc77953da588c9", "path": "HomeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f35ec3f68d4ee04d6684f7eb08ba9a2057", "path": "LotteryConfigTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3905d59a1f197af06e35146695919c9d1", "path": "LotteryOptionsTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3321c9efbbf8f145590a81e00259b7f01", "path": "LotteryOptionsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f36154b4e43878cd2063bf205754eab0b1", "path": "LotteryWheelTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a0b21853bcd3559054e7910b26a5eaa7", "path": "LotteryWheelView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f316b03661730de51af568c579f2c76f43", "path": "MainTabView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f31d733d6f85829789570a685e3d4b4f16", "path": "MemberDetailTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f38a0c7ee72d1ed5fc25e5422a410ce06e", "path": "MemberDetailView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f33f17db8ff63a27f38ba0ad6729c3d9eb", "path": "ProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3aa7215bb579713edacb12ac7fcad0bc6", "path": "SubscriptionView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f35f6b1e78c22045e7a15926de00292571", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f33125b1e4054489a2d29b43c73fbf7078", "path": "成员卡片样式优化总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "a2523ee572d34a1df6925e1e93a0e1f367451bd02f7efce6aa2b1a617958619b", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f382f539f3fe2de2505baee7fe28d8fdd2", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.plist.strings", "guid": "a2523ee572d34a1df6925e1e93a0e1f33726f91a1df1283555a020b81d2c067f", "path": "en.lproj/Localizable.strings", "regionVariantName": "en", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.strings", "guid": "a2523ee572d34a1df6925e1e93a0e1f30edd51b222b0d0f73cb40c12f137896b", "path": "zh-Hans.lproj/Localizable.strings", "regionVariantName": "zh-Hans", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f35e0e44d5a4587658b4760efce241fb89", "name": "Localizable.strings", "path": "", "sourceTree": "<group>", "type": "variantGroup"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f33c2a580a7e3051ec5fc7ff2893104e96", "path": "Persistence.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcdatamodel", "guid": "a2523ee572d34a1df6925e1e93a0e1f36c9f27a8dcac2b05a53570376d4b0224", "path": "ztt2.xcdatamodel", "sourceTree": "<group>", "type": "file"}], "fileType": "wrapper.xcdatamodeld", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d007c04f95f44b2777ecc28d6ff09df5", "name": "ztt2.xcdatamodeld", "path": "ztt2.xcdatamodeld", "sourceTree": "<group>", "type": "versionGroup"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f31ae1e8a926d1203de91e8eaf33df9967", "path": "ztt2App.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3f29ab29fce8e6a565e14563d07291ced", "name": "ztt2", "path": "ztt2", "sourceTree": "<group>", "type": "group"}, {"guid": "a2523ee572d34a1df6925e1e93a0e1f3ac5bcebb2167034ea4e1f5470b4c2e93", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f36944541524b1a644404d2e856465bfd1", "name": "ztt2", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "a2523ee572d34a1df6925e1e93a0e1f3", "path": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/我的项目/转团团/ztt2", "targets": ["TARGET@v11_hash=487d994267d06026492203e096fcc405"]}