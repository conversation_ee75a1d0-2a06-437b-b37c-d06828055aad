/Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/Objects-normal/arm64/Color+Extensions.o : /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ViewModels/LotteryToolFormData.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LiquidTabBarBackground.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LotteryToolItemConfigCard.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Persistence.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LiquidTabBarBubble.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LiquidTabBarShape.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/DateRangeType.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Styles/ResponsiveLayoutConfig.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/ztt2+CoreDataModel.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ViewModels/HomeViewModel.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ViewModels/MemberDetailViewModel.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ViewModels/GrowthDiaryViewModel.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Styles/DesignSystem.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Extensions/String+Localization.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ztt2App.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/CustomTabBar.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/DataManager.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/RedemptionRecord+CoreDataProperties.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/PointRecord+CoreDataProperties.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryRecord+CoreDataProperties.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/GlobalRule+CoreDataProperties.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/MemberRule+CoreDataProperties.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/MemberPrize+CoreDataProperties.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryConfig+CoreDataProperties.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryItem+CoreDataProperties.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/Subscription+CoreDataProperties.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/Member+CoreDataProperties.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/User+CoreDataProperties.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/AIReport+CoreDataProperties.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/DiaryEntry+CoreDataProperties.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/DataModelUsageExamples.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/MemberPointsModels.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/GeneratedAssetSymbols.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Extensions/Color+Extensions.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Models/CoreDataExtensions.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/RedemptionRecord+CoreDataClass.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/PointRecord+CoreDataClass.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryRecord+CoreDataClass.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/GlobalRule+CoreDataClass.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/MemberRule+CoreDataClass.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/MemberPrize+CoreDataClass.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryConfig+CoreDataClass.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/LotteryItem+CoreDataClass.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/Subscription+CoreDataClass.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/Member+CoreDataClass.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/User+CoreDataClass.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/AIReport+CoreDataClass.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/build/Build/Intermediates.noindex/ztt2.build/Debug-iphonesimulator/ztt2.build/DerivedSources/CoreDataGenerated/ztt2/DiaryEntry+CoreDataClass.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/MainTabView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/FamilyMemberGridView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/FamilyMemberCardView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/CustomRewardExchangeView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/MemberRewardExchangeView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/ProfileView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/HomeView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/FamilyTotalScoreView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/LotteryWheelView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/MemberDetailView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/AddRewardFormView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LotteryToolConfigFormView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/FamilyOperationFormView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/AddMemberFormView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/MemberPointsFormView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LotteryConfigMainView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/RoleSelectionView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/SubscriptionView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/LotteryToolSelectionPopupView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/MemberSelectionPopupView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/DatePickerPopupView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/MemberPickerPopupView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/DateRangePickerView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/DatePickerView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/FamilyOperationOptionsView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/MemberPointsOptionsView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/LotteryOptionsView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/Components/ActionButtonsView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/ContentView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/LotteryConfigTestView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/LotteryWheelTestView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/MemberDetailTestView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/CardComparisonTestView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/LotteryOptionsTestView.swift /Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2/Views/GrowthDiaryView.swift /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/XPC.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/ObjectiveC.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/SwiftUI.framework/Modules/SwiftUI.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/CoreData.framework/Modules/CoreData.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/simd.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/unistd.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/CoreImage.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/CoreTransferable.framework/Modules/CoreTransferable.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/_time.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/sys_time.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/SwiftUICore.framework/Modules/SwiftUICore.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/QuartzCore.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/OSLog.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/Dispatch.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/_math.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/Spatial.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/_signal.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/Metal.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/System.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/Darwin.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/CoreLocation.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/CoreFoundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/Observation.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/DataDetection.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/_stdio.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/_errno.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/FileProvider.framework/Modules/FileProvider.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/CoreGraphics.framework/Modules/CoreGraphics.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/Symbols.framework/Modules/Symbols.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/os.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/UniformTypeIdentifiers.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/_Builtin_float.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/Swift.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/UIKit.framework/Modules/UIKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/CloudKit.framework/Modules/CloudKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/_CoreData_CloudKit.framework/Modules/_CoreData_CloudKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/SwiftOnoneSupport.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/DeveloperToolsSupport.framework/Modules/DeveloperToolsSupport.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/CoreText.framework/Modules/CoreText.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/Accessibility.framework/Modules/Accessibility.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/XPC.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/ObjectiveC.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/SwiftUI.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/CoreData.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/simd.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/unistd.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/CoreImage.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/CoreTransferable.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_time.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/sys_time.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Combine.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/SwiftUICore.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/QuartzCore.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_StringProcessing.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/OSLog.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Dispatch.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_math.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Spatial.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_signal.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Metal.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/System.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Darwin.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/CoreLocation.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Foundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/CoreFoundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Observation.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/DataDetection.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_stdio.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_errno.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/FileProvider.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/CoreGraphics.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Symbols.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/os.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/UniformTypeIdentifiers.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_Builtin_float.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Swift.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/UIKit.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/CloudKit.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_CoreData_CloudKit.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/SwiftOnoneSupport.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/DeveloperToolsSupport.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/CoreText.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_Concurrency.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Accessibility.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/include/XPC.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/include/ObjectiveC.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/OpenGLES.framework/Headers/OpenGLES.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/CoreData.framework/Headers/CoreData.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/CoreImage.framework/Headers/CoreImage.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/include/_time.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/QuartzCore.framework/Headers/QuartzCore.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/include/Dispatch.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/CoreLocation.framework/Headers/CoreLocation.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/UserNotifications.framework/Headers/UserNotifications.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/include/os.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/UIKit.framework/Headers/UIKit.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/CloudKit.framework/Headers/CloudKit.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/Security.framework/Headers/Security.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins/libPreviewsMacros.dylib
