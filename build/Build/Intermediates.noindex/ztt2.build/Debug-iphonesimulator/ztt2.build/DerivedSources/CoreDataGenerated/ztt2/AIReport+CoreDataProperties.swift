//
//  AIReport+CoreDataProperties.swift
//  
//
//  Created by rainkygong on 2025/7/31.
//
//  This file was automatically generated and should not be edited.
//

import Foundation
import CoreData


extension AIReport {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<AIReport> {
        return NSFetchRequest<AIReport>(entityName: "AIReport")
    }

    @NSManaged public var id: UUID?
    @NSManaged public var title: String?
    @NSManaged public var content: String?
    @NSManaged public var reportType: String?
    @NSManaged public var createdAt: Date?
    @NSManaged public var inputDataSummary: String?
    @NSManaged public var totalRecords: Int32
    @NSManaged public var positiveRecords: Int32
    @NSManaged public var negativeRecords: Int32
    @NSManaged public var member: Member?

}

extension AIReport : Identifiable {

}
