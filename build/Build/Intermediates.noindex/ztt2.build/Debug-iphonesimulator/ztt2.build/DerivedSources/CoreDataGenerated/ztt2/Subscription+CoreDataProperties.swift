//
//  Subscription+CoreDataProperties.swift
//  
//
//  Created by rainkygong on 2025/7/31.
//
//  This file was automatically generated and should not be edited.
//

import Foundation
import CoreData


extension Subscription {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<Subscription> {
        return NSFetchRequest<Subscription>(entityName: "Subscription")
    }

    @NSManaged public var id: UUID?
    @NSManaged public var subscriptionType: String?
    @NSManaged public var isActive: Bool
    @NSManaged public var startDate: Date?
    @NSManaged public var endDate: Date?
    @NSManaged public var productIdentifier: String?
    @NSManaged public var createdAt: Date?
    @NSManaged public var updatedAt: Date?
    @NSManaged public var user: User?

}

extension Subscription : Identifiable {

}
