//
//  Member+CoreDataProperties.swift
//  
//
//  Created by rainkygong on 2025/7/31.
//
//  This file was automatically generated and should not be edited.
//

import Foundation
import CoreData


extension Member {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<Member> {
        return NSFetchRequest<Member>(entityName: "Member")
    }

    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var role: String?
    @NSManaged public var birthDate: Date?
    @NSManaged public var memberNumber: Int32
    @NSManaged public var currentPoints: Int32
    @NSManaged public var avatar: String?
    @NSManaged public var createdAt: Date?
    @NSManaged public var updatedAt: Date?
    @NSManaged public var user: User?
    @NSManaged public var pointRecords: NSSet?
    @NSManaged public var diaryEntries: NSSet?
    @NSManaged public var redemptionRecords: NSSet?
    @NSManaged public var lotteryRecords: NSSet?
    @NSManaged public var aiReports: NSSet?
    @NSManaged public var memberRules: NSSet?
    @NSManaged public var memberPrizes: NSSet?
    @NSManaged public var lotteryConfigs: NSSet?

}

// MARK: Generated accessors for pointRecords
extension Member {

    @objc(addPointRecordsObject:)
    @NSManaged public func addToPointRecords(_ value: PointRecord)

    @objc(removePointRecordsObject:)
    @NSManaged public func removeFromPointRecords(_ value: PointRecord)

    @objc(addPointRecords:)
    @NSManaged public func addToPointRecords(_ values: NSSet)

    @objc(removePointRecords:)
    @NSManaged public func removeFromPointRecords(_ values: NSSet)

}

// MARK: Generated accessors for diaryEntries
extension Member {

    @objc(addDiaryEntriesObject:)
    @NSManaged public func addToDiaryEntries(_ value: DiaryEntry)

    @objc(removeDiaryEntriesObject:)
    @NSManaged public func removeFromDiaryEntries(_ value: DiaryEntry)

    @objc(addDiaryEntries:)
    @NSManaged public func addToDiaryEntries(_ values: NSSet)

    @objc(removeDiaryEntries:)
    @NSManaged public func removeFromDiaryEntries(_ values: NSSet)

}

// MARK: Generated accessors for redemptionRecords
extension Member {

    @objc(addRedemptionRecordsObject:)
    @NSManaged public func addToRedemptionRecords(_ value: RedemptionRecord)

    @objc(removeRedemptionRecordsObject:)
    @NSManaged public func removeFromRedemptionRecords(_ value: RedemptionRecord)

    @objc(addRedemptionRecords:)
    @NSManaged public func addToRedemptionRecords(_ values: NSSet)

    @objc(removeRedemptionRecords:)
    @NSManaged public func removeFromRedemptionRecords(_ values: NSSet)

}

// MARK: Generated accessors for lotteryRecords
extension Member {

    @objc(addLotteryRecordsObject:)
    @NSManaged public func addToLotteryRecords(_ value: LotteryRecord)

    @objc(removeLotteryRecordsObject:)
    @NSManaged public func removeFromLotteryRecords(_ value: LotteryRecord)

    @objc(addLotteryRecords:)
    @NSManaged public func addToLotteryRecords(_ values: NSSet)

    @objc(removeLotteryRecords:)
    @NSManaged public func removeFromLotteryRecords(_ values: NSSet)

}

// MARK: Generated accessors for aiReports
extension Member {

    @objc(addAiReportsObject:)
    @NSManaged public func addToAiReports(_ value: AIReport)

    @objc(removeAiReportsObject:)
    @NSManaged public func removeFromAiReports(_ value: AIReport)

    @objc(addAiReports:)
    @NSManaged public func addToAiReports(_ values: NSSet)

    @objc(removeAiReports:)
    @NSManaged public func removeFromAiReports(_ values: NSSet)

}

// MARK: Generated accessors for memberRules
extension Member {

    @objc(addMemberRulesObject:)
    @NSManaged public func addToMemberRules(_ value: MemberRule)

    @objc(removeMemberRulesObject:)
    @NSManaged public func removeFromMemberRules(_ value: MemberRule)

    @objc(addMemberRules:)
    @NSManaged public func addToMemberRules(_ values: NSSet)

    @objc(removeMemberRules:)
    @NSManaged public func removeFromMemberRules(_ values: NSSet)

}

// MARK: Generated accessors for memberPrizes
extension Member {

    @objc(addMemberPrizesObject:)
    @NSManaged public func addToMemberPrizes(_ value: MemberPrize)

    @objc(removeMemberPrizesObject:)
    @NSManaged public func removeFromMemberPrizes(_ value: MemberPrize)

    @objc(addMemberPrizes:)
    @NSManaged public func addToMemberPrizes(_ values: NSSet)

    @objc(removeMemberPrizes:)
    @NSManaged public func removeFromMemberPrizes(_ values: NSSet)

}

// MARK: Generated accessors for lotteryConfigs
extension Member {

    @objc(addLotteryConfigsObject:)
    @NSManaged public func addToLotteryConfigs(_ value: LotteryConfig)

    @objc(removeLotteryConfigsObject:)
    @NSManaged public func removeFromLotteryConfigs(_ value: LotteryConfig)

    @objc(addLotteryConfigs:)
    @NSManaged public func addToLotteryConfigs(_ values: NSSet)

    @objc(removeLotteryConfigs:)
    @NSManaged public func removeFromLotteryConfigs(_ values: NSSet)

}

extension Member : Identifiable {

}
