//
//  User+CoreDataProperties.swift
//  
//
//  Created by rainkygong on 2025/7/31.
//
//  This file was automatically generated and should not be edited.
//

import Foundation
import CoreData


extension User {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<User> {
        return NSFetchRequest<User>(entityName: "User")
    }

    @NSManaged public var id: UUID?
    @NSManaged public var nickname: String?
    @NSManaged public var email: String?
    @NSManaged public var appleUserID: String?
    @NSManaged public var createdAt: Date?
    @NSManaged public var subscription: Subscription?
    @NSManaged public var members: NSSet?
    @NSManaged public var globalRules: NSSet?

}

// MARK: Generated accessors for members
extension User {

    @objc(addMembersObject:)
    @NSManaged public func addToMembers(_ value: Member)

    @objc(removeMembersObject:)
    @NSManaged public func removeFromMembers(_ value: Member)

    @objc(addMembers:)
    @NSManaged public func addToMembers(_ values: NSSet)

    @objc(removeMembers:)
    @NSManaged public func removeFromMembers(_ values: NSSet)

}

// MARK: Generated accessors for globalRules
extension User {

    @objc(addGlobalRulesObject:)
    @NSManaged public func addToGlobalRules(_ value: GlobalRule)

    @objc(removeGlobalRulesObject:)
    @NSManaged public func removeFromGlobalRules(_ value: GlobalRule)

    @objc(addGlobalRules:)
    @NSManaged public func addToGlobalRules(_ values: NSSet)

    @objc(removeGlobalRules:)
    @NSManaged public func removeFromGlobalRules(_ values: NSSet)

}

extension User : Identifiable {

}
