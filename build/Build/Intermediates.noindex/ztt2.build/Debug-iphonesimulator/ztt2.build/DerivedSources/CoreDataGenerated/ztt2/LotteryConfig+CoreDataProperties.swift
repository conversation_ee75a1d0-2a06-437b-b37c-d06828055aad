//
//  LotteryConfig+CoreDataProperties.swift
//  
//
//  Created by rainkygong on 2025/7/31.
//
//  This file was automatically generated and should not be edited.
//

import Foundation
import CoreData


extension LotteryConfig {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<LotteryConfig> {
        return NSFetchRequest<LotteryConfig>(entityName: "LotteryConfig")
    }

    @NSManaged public var id: UUID?
    @NSManaged public var toolType: String?
    @NSManaged public var itemCount: Int32
    @NSManaged public var costPerPlay: Int32
    @NSManaged public var createdAt: Date?
    @NSManaged public var updatedAt: Date?
    @NSManaged public var member: Member?
    @NSManaged public var items: NSSet?

}

// MARK: Generated accessors for items
extension LotteryConfig {

    @objc(addItemsObject:)
    @NSManaged public func addToItems(_ value: LotteryItem)

    @objc(removeItemsObject:)
    @NSManaged public func removeFromItems(_ value: LotteryItem)

    @objc(addItems:)
    @NSManaged public func addToItems(_ values: NSSet)

    @objc(removeItems:)
    @NSManaged public func removeFromItems(_ values: NSSet)

}

extension LotteryConfig : Identifiable {

}
