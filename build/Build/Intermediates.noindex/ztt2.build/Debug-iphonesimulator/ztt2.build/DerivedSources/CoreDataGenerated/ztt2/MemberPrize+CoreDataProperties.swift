//
//  MemberPrize+CoreDataProperties.swift
//  
//
//  Created by rainkygong on 2025/7/31.
//
//  This file was automatically generated and should not be edited.
//

import Foundation
import CoreData


extension MemberPrize {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<MemberPrize> {
        return NSFetchRequest<MemberPrize>(entityName: "MemberPrize")
    }

    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var cost: Int32
    @NSManaged public var type: String?
    @NSManaged public var createdAt: Date?
    @NSManaged public var member: Member?

}

extension MemberPrize : Identifiable {

}
