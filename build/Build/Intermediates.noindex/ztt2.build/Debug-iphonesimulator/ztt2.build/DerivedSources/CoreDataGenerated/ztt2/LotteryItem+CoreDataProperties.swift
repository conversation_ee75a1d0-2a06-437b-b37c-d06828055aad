//
//  LotteryItem+CoreDataProperties.swift
//  
//
//  Created by rainkygong on 2025/7/31.
//
//  This file was automatically generated and should not be edited.
//

import Foundation
import CoreData


extension LotteryItem {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<LotteryItem> {
        return NSFetchRequest<LotteryItem>(entityName: "LotteryItem")
    }

    @NSManaged public var id: UUID?
    @NSManaged public var itemIndex: Int32
    @NSManaged public var prizeName: String?
    @NSManaged public var createdAt: Date?
    @NSManaged public var lotteryConfig: LotteryConfig?

}

extension LotteryItem : Identifiable {

}
