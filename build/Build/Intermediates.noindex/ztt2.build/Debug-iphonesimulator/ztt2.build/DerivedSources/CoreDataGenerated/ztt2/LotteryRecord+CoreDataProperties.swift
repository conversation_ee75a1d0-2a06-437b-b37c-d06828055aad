//
//  LotteryRecord+CoreDataProperties.swift
//  
//
//  Created by rainkygong on 2025/7/31.
//
//  This file was automatically generated and should not be edited.
//

import Foundation
import CoreData


extension LotteryRecord {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<LotteryRecord> {
        return NSFetchRequest<LotteryRecord>(entityName: "LotteryRecord")
    }

    @NSManaged public var id: UUID?
    @NSManaged public var toolType: String?
    @NSManaged public var prizeResult: String?
    @NSManaged public var cost: Int32
    @NSManaged public var timestamp: Date?
    @NSManaged public var member: Member?

}

extension LotteryRecord : Identifiable {

}
