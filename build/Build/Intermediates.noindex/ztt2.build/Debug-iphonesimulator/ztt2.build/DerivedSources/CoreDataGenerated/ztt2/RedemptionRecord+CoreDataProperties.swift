//
//  RedemptionRecord+CoreDataProperties.swift
//  
//
//  Created by rainkygong on 2025/7/31.
//
//  This file was automatically generated and should not be edited.
//

import Foundation
import CoreData


extension RedemptionRecord {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<RedemptionRecord> {
        return NSFetchRequest<RedemptionRecord>(entityName: "RedemptionRecord")
    }

    @NSManaged public var id: UUID?
    @NSManaged public var prizeName: String?
    @NSManaged public var cost: Int32
    @NSManaged public var timestamp: Date?
    @NSManaged public var source: String?
    @NSManaged public var member: Member?

}

extension RedemptionRecord : Identifiable {

}
