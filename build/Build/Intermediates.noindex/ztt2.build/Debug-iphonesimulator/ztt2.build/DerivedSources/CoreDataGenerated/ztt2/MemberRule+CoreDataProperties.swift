//
//  MemberRule+CoreDataProperties.swift
//  
//
//  Created by rainkygong on 2025/7/31.
//
//  This file was automatically generated and should not be edited.
//

import Foundation
import CoreData


extension MemberRule {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<MemberRule> {
        return NSFetchRequest<MemberRule>(entityName: "MemberRule")
    }

    @NSManaged public var id: UUID?
    @NSManaged public var name: String?
    @NSManaged public var value: Int32
    @NSManaged public var type: String?
    @NSManaged public var isFrequent: Bool
    @NSManaged public var createdAt: Date?
    @NSManaged public var member: Member?

}

extension MemberRule : Identifiable {

}
