//
//  PointRecord+CoreDataProperties.swift
//  
//
//  Created by rainkygong on 2025/7/31.
//
//  This file was automatically generated and should not be edited.
//

import Foundation
import CoreData


extension PointRecord {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<PointRecord> {
        return NSFetchRequest<PointRecord>(entityName: "PointRecord")
    }

    @NSManaged public var id: UUID?
    @NSManaged public var reason: String?
    @NSManaged public var value: Int32
    @NSManaged public var timestamp: Date?
    @NSManaged public var recordType: String?
    @NSManaged public var isReversed: Bool
    @NSManaged public var member: Member?

}

extension PointRecord : Identifiable {

}
