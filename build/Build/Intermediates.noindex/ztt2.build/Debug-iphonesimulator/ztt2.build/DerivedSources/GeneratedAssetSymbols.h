#import <Foundation/Foundation.h>

#if __has_attribute(swift_private)
#define AC_SWIFT_PRIVATE __attribute__((swift_private))
#else
#define AC_SWIFT_PRIVATE
#endif

/// The "banjixinxi" asset catalog image resource.
static NSString * const ACImageNameBanjixinxi AC_SWIFT_PRIVATE = @"banjixinxi";

/// The "chengzhang" asset catalog image resource.
static NSString * const ACImageNameChengzhang AC_SWIFT_PRIVATE = @"chengzhang";

/// The "choujiang" asset catalog image resource.
static NSString * const ACImageNameChoujiang AC_SWIFT_PRIVATE = @"choujiang";

/// The "denglu" asset catalog image resource.
static NSString * const ACImageNameDenglu AC_SWIFT_PRIVATE = @"denglu";

/// The "fanhui" asset catalog image resource.
static NSString * const ACImageNameFanhui AC_SWIFT_PRIVATE = @"fanhui";

/// The "fenxi" asset catalog image resource.
static NSString * const ACImageNameFenxi AC_SWIFT_PRIVATE = @"fenxi";

/// The "guanyu" asset catalog image resource.
static NSString * const ACImageNameGuanyu AC_SWIFT_PRIVATE = @"guanyu";

/// The "guizepeizhi" asset catalog image resource.
static NSString * const ACImageNameGuizepeizhi AC_SWIFT_PRIVATE = @"guizepeizhi";

/// The "guzhangfankui" asset catalog image resource.
static NSString * const ACImageNameGuzhangfankui AC_SWIFT_PRIVATE = @"guzhangfankui";

/// The "huiyuan" asset catalog image resource.
static NSString * const ACImageNameHuiyuan AC_SWIFT_PRIVATE = @"huiyuan";

/// The "jianpan" asset catalog image resource.
static NSString * const ACImageNameJianpan AC_SWIFT_PRIVATE = @"jianpan";

/// The "laoshi" asset catalog image resource.
static NSString * const ACImageNameLaoshi AC_SWIFT_PRIVATE = @"laoshi";

/// The "lingqujilu" asset catalog image resource.
static NSString * const ACImageNameLingqujilu AC_SWIFT_PRIVATE = @"lingqujilu";

/// The "lishi" asset catalog image resource.
static NSString * const ACImageNameLishi AC_SWIFT_PRIVATE = @"lishi";

/// The "logo" asset catalog image resource.
static NSString * const ACImageNameLogo AC_SWIFT_PRIVATE = @"logo";

/// The "shanchu" asset catalog image resource.
static NSString * const ACImageNameShanchu AC_SWIFT_PRIVATE = @"shanchu";

/// The "shezhi" asset catalog image resource.
static NSString * const ACImageNameShezhi AC_SWIFT_PRIVATE = @"shezhi";

/// The "shouye1_1" asset catalog image resource.
static NSString * const ACImageNameShouye11 AC_SWIFT_PRIVATE = @"shouye1_1";

/// The "sousuo" asset catalog image resource.
static NSString * const ACImageNameSousuo AC_SWIFT_PRIVATE = @"sousuo";

/// The "tuichu" asset catalog image resource.
static NSString * const ACImageNameTuichu AC_SWIFT_PRIVATE = @"tuichu";

/// The "wode13" asset catalog image resource.
static NSString * const ACImageNameWode13 AC_SWIFT_PRIVATE = @"wode13";

/// The "yinsizhengce" asset catalog image resource.
static NSString * const ACImageNameYinsizhengce AC_SWIFT_PRIVATE = @"yinsizhengce";

/// The "yonghuxieyi" asset catalog image resource.
static NSString * const ACImageNameYonghuxieyi AC_SWIFT_PRIVATE = @"yonghuxieyi";

/// The "yuyan" asset catalog image resource.
static NSString * const ACImageNameYuyan AC_SWIFT_PRIVATE = @"yuyan";

#warning The "个人中心插图" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "全班操作" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "关闭" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "其他头像" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "初级会员" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "刮刮卡" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "刮刮卡中奖" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "女生头像" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "妈妈头像" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "宝箱已打开" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "宝箱未打开" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "录音" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "添加学生" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "烟花" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "爸爸头像" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "男生头像" image asset name resolves to an empty symbol. Try renaming the asset.

/// The "登录页面logo" asset catalog image resource.
static NSString * const ACImageNamelogo AC_SWIFT_PRIVATE = @"登录页面logo";

#warning The "皇冠" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "皇冠(订阅）" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "进入" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "道具配置" image asset name resolves to an empty symbol. Try renaming the asset.

#warning The "高级会员" image asset name resolves to an empty symbol. Try renaming the asset.

#undef AC_SWIFT_PRIVATE
