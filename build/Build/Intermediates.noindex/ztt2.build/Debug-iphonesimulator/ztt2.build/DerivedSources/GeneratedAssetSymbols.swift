import Foundation
#if canImport(AppKit)
import AppKit
#endif
#if canImport(UIKit)
import UIKit
#endif
#if canImport(SwiftUI)
import Swift<PERSON>
#endif
#if canImport(DeveloperToolsSupport)
import DeveloperToolsSupport
#endif

#if SWIFT_PACKAGE
private let resourceBundle = Foundation.Bundle.module
#else
private class ResourceBundleClass {}
private let resourceBundle = Foundation.Bundle(for: ResourceBundleClass.self)
#endif

// MARK: - Color Symbols -

@available(iOS 11.0, macOS 10.13, tvOS 11.0, *)
extension ColorResource {

}

// MARK: - Image Symbols -

@available(iOS 11.0, macOS 10.7, tvOS 11.0, *)
extension ImageResource {

    /// The "banjixinxi" asset catalog image resource.
    static let banjixinxi = ImageResource(name: "banjixinxi", bundle: resourceBundle)

    /// The "chengzhang" asset catalog image resource.
    static let chengzhang = ImageResource(name: "chengzhang", bundle: resourceBundle)

    /// The "choujiang" asset catalog image resource.
    static let choujiang = ImageResource(name: "choujiang", bundle: resourceBundle)

    /// The "denglu" asset catalog image resource.
    static let denglu = ImageResource(name: "denglu", bundle: resourceBundle)

    /// The "fanhui" asset catalog image resource.
    static let fanhui = ImageResource(name: "fanhui", bundle: resourceBundle)

    /// The "fenxi" asset catalog image resource.
    static let fenxi = ImageResource(name: "fenxi", bundle: resourceBundle)

    /// The "guanyu" asset catalog image resource.
    static let guanyu = ImageResource(name: "guanyu", bundle: resourceBundle)

    /// The "guizepeizhi" asset catalog image resource.
    static let guizepeizhi = ImageResource(name: "guizepeizhi", bundle: resourceBundle)

    /// The "guzhangfankui" asset catalog image resource.
    static let guzhangfankui = ImageResource(name: "guzhangfankui", bundle: resourceBundle)

    /// The "huiyuan" asset catalog image resource.
    static let huiyuan = ImageResource(name: "huiyuan", bundle: resourceBundle)

    /// The "jianpan" asset catalog image resource.
    static let jianpan = ImageResource(name: "jianpan", bundle: resourceBundle)

    /// The "laoshi" asset catalog image resource.
    static let laoshi = ImageResource(name: "laoshi", bundle: resourceBundle)

    /// The "lingqujilu" asset catalog image resource.
    static let lingqujilu = ImageResource(name: "lingqujilu", bundle: resourceBundle)

    /// The "lishi" asset catalog image resource.
    static let lishi = ImageResource(name: "lishi", bundle: resourceBundle)

    /// The "logo" asset catalog image resource.
    static let logo = ImageResource(name: "logo", bundle: resourceBundle)

    /// The "shanchu" asset catalog image resource.
    static let shanchu = ImageResource(name: "shanchu", bundle: resourceBundle)

    /// The "shezhi" asset catalog image resource.
    static let shezhi = ImageResource(name: "shezhi", bundle: resourceBundle)

    /// The "shouye1_1" asset catalog image resource.
    static let shouye11 = ImageResource(name: "shouye1_1", bundle: resourceBundle)

    /// The "sousuo" asset catalog image resource.
    static let sousuo = ImageResource(name: "sousuo", bundle: resourceBundle)

    /// The "tuichu" asset catalog image resource.
    static let tuichu = ImageResource(name: "tuichu", bundle: resourceBundle)

    /// The "wode13" asset catalog image resource.
    static let wode13 = ImageResource(name: "wode13", bundle: resourceBundle)

    /// The "yinsizhengce" asset catalog image resource.
    static let yinsizhengce = ImageResource(name: "yinsizhengce", bundle: resourceBundle)

    /// The "yonghuxieyi" asset catalog image resource.
    static let yonghuxieyi = ImageResource(name: "yonghuxieyi", bundle: resourceBundle)

    /// The "yuyan" asset catalog image resource.
    static let yuyan = ImageResource(name: "yuyan", bundle: resourceBundle)

    /// The "个人中心插图" asset catalog image resource.
    static let 个人中心插图 = ImageResource(name: "个人中心插图", bundle: resourceBundle)

    /// The "全班操作" asset catalog image resource.
    static let 全班操作 = ImageResource(name: "全班操作", bundle: resourceBundle)

    /// The "关闭" asset catalog image resource.
    static let 关闭 = ImageResource(name: "关闭", bundle: resourceBundle)

    /// The "其他头像" asset catalog image resource.
    static let 其他头像 = ImageResource(name: "其他头像", bundle: resourceBundle)

    /// The "初级会员" asset catalog image resource.
    static let 初级会员 = ImageResource(name: "初级会员", bundle: resourceBundle)

    /// The "刮刮卡" asset catalog image resource.
    static let 刮刮卡 = ImageResource(name: "刮刮卡", bundle: resourceBundle)

    /// The "刮刮卡中奖" asset catalog image resource.
    static let 刮刮卡中奖 = ImageResource(name: "刮刮卡中奖", bundle: resourceBundle)

    /// The "女生头像" asset catalog image resource.
    static let 女生头像 = ImageResource(name: "女生头像", bundle: resourceBundle)

    /// The "妈妈头像" asset catalog image resource.
    static let 妈妈头像 = ImageResource(name: "妈妈头像", bundle: resourceBundle)

    /// The "宝箱已打开" asset catalog image resource.
    static let 宝箱已打开 = ImageResource(name: "宝箱已打开", bundle: resourceBundle)

    /// The "宝箱未打开" asset catalog image resource.
    static let 宝箱未打开 = ImageResource(name: "宝箱未打开", bundle: resourceBundle)

    /// The "录音" asset catalog image resource.
    static let 录音 = ImageResource(name: "录音", bundle: resourceBundle)

    /// The "添加学生" asset catalog image resource.
    static let 添加学生 = ImageResource(name: "添加学生", bundle: resourceBundle)

    /// The "烟花" asset catalog image resource.
    static let 烟花 = ImageResource(name: "烟花", bundle: resourceBundle)

    /// The "爸爸头像" asset catalog image resource.
    static let 爸爸头像 = ImageResource(name: "爸爸头像", bundle: resourceBundle)

    /// The "男生头像" asset catalog image resource.
    static let 男生头像 = ImageResource(name: "男生头像", bundle: resourceBundle)

    /// The "登录页面logo" asset catalog image resource.
    static let 登录页面logo = ImageResource(name: "登录页面logo", bundle: resourceBundle)

    /// The "皇冠" asset catalog image resource.
    static let 皇冠 = ImageResource(name: "皇冠", bundle: resourceBundle)

    /// The "皇冠(订阅）" asset catalog image resource.
    static let 皇冠订阅 = ImageResource(name: "皇冠(订阅）", bundle: resourceBundle)

    /// The "进入" asset catalog image resource.
    static let 进入 = ImageResource(name: "进入", bundle: resourceBundle)

    /// The "道具配置" asset catalog image resource.
    static let 道具配置 = ImageResource(name: "道具配置", bundle: resourceBundle)

    /// The "高级会员" asset catalog image resource.
    static let 高级会员 = ImageResource(name: "高级会员", bundle: resourceBundle)

}

// MARK: - Color Symbol Extensions -

#if canImport(AppKit)
@available(macOS 10.13, *)
@available(macCatalyst, unavailable)
extension AppKit.NSColor {

}
#endif

#if canImport(UIKit)
@available(iOS 11.0, tvOS 11.0, *)
@available(watchOS, unavailable)
extension UIKit.UIColor {

}
#endif

#if canImport(SwiftUI)
@available(iOS 13.0, macOS 10.15, tvOS 13.0, watchOS 6.0, *)
extension SwiftUI.Color {

}

@available(iOS 13.0, macOS 10.15, tvOS 13.0, watchOS 6.0, *)
extension SwiftUI.ShapeStyle where Self == SwiftUI.Color {

}
#endif

// MARK: - Image Symbol Extensions -

#if canImport(AppKit)
@available(macOS 10.7, *)
@available(macCatalyst, unavailable)
extension AppKit.NSImage {

    /// The "banjixinxi" asset catalog image.
    static var banjixinxi: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .banjixinxi)
#else
        .init()
#endif
    }

    /// The "chengzhang" asset catalog image.
    static var chengzhang: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .chengzhang)
#else
        .init()
#endif
    }

    /// The "choujiang" asset catalog image.
    static var choujiang: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .choujiang)
#else
        .init()
#endif
    }

    /// The "denglu" asset catalog image.
    static var denglu: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .denglu)
#else
        .init()
#endif
    }

    /// The "fanhui" asset catalog image.
    static var fanhui: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .fanhui)
#else
        .init()
#endif
    }

    /// The "fenxi" asset catalog image.
    static var fenxi: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .fenxi)
#else
        .init()
#endif
    }

    /// The "guanyu" asset catalog image.
    static var guanyu: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .guanyu)
#else
        .init()
#endif
    }

    /// The "guizepeizhi" asset catalog image.
    static var guizepeizhi: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .guizepeizhi)
#else
        .init()
#endif
    }

    /// The "guzhangfankui" asset catalog image.
    static var guzhangfankui: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .guzhangfankui)
#else
        .init()
#endif
    }

    /// The "huiyuan" asset catalog image.
    static var huiyuan: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .huiyuan)
#else
        .init()
#endif
    }

    /// The "jianpan" asset catalog image.
    static var jianpan: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .jianpan)
#else
        .init()
#endif
    }

    /// The "laoshi" asset catalog image.
    static var laoshi: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .laoshi)
#else
        .init()
#endif
    }

    /// The "lingqujilu" asset catalog image.
    static var lingqujilu: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .lingqujilu)
#else
        .init()
#endif
    }

    /// The "lishi" asset catalog image.
    static var lishi: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .lishi)
#else
        .init()
#endif
    }

    /// The "logo" asset catalog image.
    static var logo: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .logo)
#else
        .init()
#endif
    }

    /// The "shanchu" asset catalog image.
    static var shanchu: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .shanchu)
#else
        .init()
#endif
    }

    /// The "shezhi" asset catalog image.
    static var shezhi: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .shezhi)
#else
        .init()
#endif
    }

    /// The "shouye1_1" asset catalog image.
    static var shouye11: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .shouye11)
#else
        .init()
#endif
    }

    /// The "sousuo" asset catalog image.
    static var sousuo: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .sousuo)
#else
        .init()
#endif
    }

    /// The "tuichu" asset catalog image.
    static var tuichu: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .tuichu)
#else
        .init()
#endif
    }

    /// The "wode13" asset catalog image.
    static var wode13: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .wode13)
#else
        .init()
#endif
    }

    /// The "yinsizhengce" asset catalog image.
    static var yinsizhengce: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .yinsizhengce)
#else
        .init()
#endif
    }

    /// The "yonghuxieyi" asset catalog image.
    static var yonghuxieyi: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .yonghuxieyi)
#else
        .init()
#endif
    }

    /// The "yuyan" asset catalog image.
    static var yuyan: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .yuyan)
#else
        .init()
#endif
    }

    /// The "个人中心插图" asset catalog image.
    static var 个人中心插图: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .个人中心插图)
#else
        .init()
#endif
    }

    /// The "全班操作" asset catalog image.
    static var 全班操作: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .全班操作)
#else
        .init()
#endif
    }

    /// The "关闭" asset catalog image.
    static var 关闭: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .关闭)
#else
        .init()
#endif
    }

    /// The "其他头像" asset catalog image.
    static var 其他头像: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .其他头像)
#else
        .init()
#endif
    }

    /// The "初级会员" asset catalog image.
    static var 初级会员: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .初级会员)
#else
        .init()
#endif
    }

    /// The "刮刮卡" asset catalog image.
    static var 刮刮卡: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .刮刮卡)
#else
        .init()
#endif
    }

    /// The "刮刮卡中奖" asset catalog image.
    static var 刮刮卡中奖: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .刮刮卡中奖)
#else
        .init()
#endif
    }

    /// The "女生头像" asset catalog image.
    static var 女生头像: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .女生头像)
#else
        .init()
#endif
    }

    /// The "妈妈头像" asset catalog image.
    static var 妈妈头像: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .妈妈头像)
#else
        .init()
#endif
    }

    /// The "宝箱已打开" asset catalog image.
    static var 宝箱已打开: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .宝箱已打开)
#else
        .init()
#endif
    }

    /// The "宝箱未打开" asset catalog image.
    static var 宝箱未打开: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .宝箱未打开)
#else
        .init()
#endif
    }

    /// The "录音" asset catalog image.
    static var 录音: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .录音)
#else
        .init()
#endif
    }

    /// The "添加学生" asset catalog image.
    static var 添加学生: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .添加学生)
#else
        .init()
#endif
    }

    /// The "烟花" asset catalog image.
    static var 烟花: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .烟花)
#else
        .init()
#endif
    }

    /// The "爸爸头像" asset catalog image.
    static var 爸爸头像: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .爸爸头像)
#else
        .init()
#endif
    }

    /// The "男生头像" asset catalog image.
    static var 男生头像: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .男生头像)
#else
        .init()
#endif
    }

    /// The "登录页面logo" asset catalog image.
    static var 登录页面logo: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .登录页面logo)
#else
        .init()
#endif
    }

    /// The "皇冠" asset catalog image.
    static var 皇冠: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .皇冠)
#else
        .init()
#endif
    }

    /// The "皇冠(订阅）" asset catalog image.
    static var 皇冠订阅: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .皇冠订阅)
#else
        .init()
#endif
    }

    /// The "进入" asset catalog image.
    static var 进入: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .进入)
#else
        .init()
#endif
    }

    /// The "道具配置" asset catalog image.
    static var 道具配置: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .道具配置)
#else
        .init()
#endif
    }

    /// The "高级会员" asset catalog image.
    static var 高级会员: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .高级会员)
#else
        .init()
#endif
    }

}
#endif

#if canImport(UIKit)
@available(iOS 11.0, tvOS 11.0, *)
@available(watchOS, unavailable)
extension UIKit.UIImage {

    /// The "banjixinxi" asset catalog image.
    static var banjixinxi: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .banjixinxi)
#else
        .init()
#endif
    }

    /// The "chengzhang" asset catalog image.
    static var chengzhang: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .chengzhang)
#else
        .init()
#endif
    }

    /// The "choujiang" asset catalog image.
    static var choujiang: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .choujiang)
#else
        .init()
#endif
    }

    /// The "denglu" asset catalog image.
    static var denglu: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .denglu)
#else
        .init()
#endif
    }

    /// The "fanhui" asset catalog image.
    static var fanhui: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .fanhui)
#else
        .init()
#endif
    }

    /// The "fenxi" asset catalog image.
    static var fenxi: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .fenxi)
#else
        .init()
#endif
    }

    /// The "guanyu" asset catalog image.
    static var guanyu: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .guanyu)
#else
        .init()
#endif
    }

    /// The "guizepeizhi" asset catalog image.
    static var guizepeizhi: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .guizepeizhi)
#else
        .init()
#endif
    }

    /// The "guzhangfankui" asset catalog image.
    static var guzhangfankui: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .guzhangfankui)
#else
        .init()
#endif
    }

    /// The "huiyuan" asset catalog image.
    static var huiyuan: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .huiyuan)
#else
        .init()
#endif
    }

    /// The "jianpan" asset catalog image.
    static var jianpan: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .jianpan)
#else
        .init()
#endif
    }

    /// The "laoshi" asset catalog image.
    static var laoshi: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .laoshi)
#else
        .init()
#endif
    }

    /// The "lingqujilu" asset catalog image.
    static var lingqujilu: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .lingqujilu)
#else
        .init()
#endif
    }

    /// The "lishi" asset catalog image.
    static var lishi: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .lishi)
#else
        .init()
#endif
    }

    /// The "logo" asset catalog image.
    static var logo: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .logo)
#else
        .init()
#endif
    }

    /// The "shanchu" asset catalog image.
    static var shanchu: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .shanchu)
#else
        .init()
#endif
    }

    /// The "shezhi" asset catalog image.
    static var shezhi: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .shezhi)
#else
        .init()
#endif
    }

    /// The "shouye1_1" asset catalog image.
    static var shouye11: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .shouye11)
#else
        .init()
#endif
    }

    /// The "sousuo" asset catalog image.
    static var sousuo: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .sousuo)
#else
        .init()
#endif
    }

    /// The "tuichu" asset catalog image.
    static var tuichu: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .tuichu)
#else
        .init()
#endif
    }

    /// The "wode13" asset catalog image.
    static var wode13: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .wode13)
#else
        .init()
#endif
    }

    /// The "yinsizhengce" asset catalog image.
    static var yinsizhengce: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .yinsizhengce)
#else
        .init()
#endif
    }

    /// The "yonghuxieyi" asset catalog image.
    static var yonghuxieyi: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .yonghuxieyi)
#else
        .init()
#endif
    }

    /// The "yuyan" asset catalog image.
    static var yuyan: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .yuyan)
#else
        .init()
#endif
    }

    /// The "个人中心插图" asset catalog image.
    static var 个人中心插图: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .个人中心插图)
#else
        .init()
#endif
    }

    /// The "全班操作" asset catalog image.
    static var 全班操作: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .全班操作)
#else
        .init()
#endif
    }

    /// The "关闭" asset catalog image.
    static var 关闭: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .关闭)
#else
        .init()
#endif
    }

    /// The "其他头像" asset catalog image.
    static var 其他头像: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .其他头像)
#else
        .init()
#endif
    }

    /// The "初级会员" asset catalog image.
    static var 初级会员: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .初级会员)
#else
        .init()
#endif
    }

    /// The "刮刮卡" asset catalog image.
    static var 刮刮卡: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .刮刮卡)
#else
        .init()
#endif
    }

    /// The "刮刮卡中奖" asset catalog image.
    static var 刮刮卡中奖: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .刮刮卡中奖)
#else
        .init()
#endif
    }

    /// The "女生头像" asset catalog image.
    static var 女生头像: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .女生头像)
#else
        .init()
#endif
    }

    /// The "妈妈头像" asset catalog image.
    static var 妈妈头像: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .妈妈头像)
#else
        .init()
#endif
    }

    /// The "宝箱已打开" asset catalog image.
    static var 宝箱已打开: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .宝箱已打开)
#else
        .init()
#endif
    }

    /// The "宝箱未打开" asset catalog image.
    static var 宝箱未打开: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .宝箱未打开)
#else
        .init()
#endif
    }

    /// The "录音" asset catalog image.
    static var 录音: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .录音)
#else
        .init()
#endif
    }

    /// The "添加学生" asset catalog image.
    static var 添加学生: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .添加学生)
#else
        .init()
#endif
    }

    /// The "烟花" asset catalog image.
    static var 烟花: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .烟花)
#else
        .init()
#endif
    }

    /// The "爸爸头像" asset catalog image.
    static var 爸爸头像: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .爸爸头像)
#else
        .init()
#endif
    }

    /// The "男生头像" asset catalog image.
    static var 男生头像: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .男生头像)
#else
        .init()
#endif
    }

    /// The "登录页面logo" asset catalog image.
    static var 登录页面logo: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .登录页面logo)
#else
        .init()
#endif
    }

    /// The "皇冠" asset catalog image.
    static var 皇冠: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .皇冠)
#else
        .init()
#endif
    }

    /// The "皇冠(订阅）" asset catalog image.
    static var 皇冠订阅: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .皇冠订阅)
#else
        .init()
#endif
    }

    /// The "进入" asset catalog image.
    static var 进入: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .进入)
#else
        .init()
#endif
    }

    /// The "道具配置" asset catalog image.
    static var 道具配置: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .道具配置)
#else
        .init()
#endif
    }

    /// The "高级会员" asset catalog image.
    static var 高级会员: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .高级会员)
#else
        .init()
#endif
    }

}
#endif

// MARK: - Thinnable Asset Support -

@available(iOS 11.0, macOS 10.13, tvOS 11.0, *)
@available(watchOS, unavailable)
extension ColorResource {

    private init?(thinnableName: Swift.String, bundle: Foundation.Bundle) {
#if canImport(AppKit) && os(macOS)
        if AppKit.NSColor(named: NSColor.Name(thinnableName), bundle: bundle) != nil {
            self.init(name: thinnableName, bundle: bundle)
        } else {
            return nil
        }
#elseif canImport(UIKit) && !os(watchOS)
        if UIKit.UIColor(named: thinnableName, in: bundle, compatibleWith: nil) != nil {
            self.init(name: thinnableName, bundle: bundle)
        } else {
            return nil
        }
#else
        return nil
#endif
    }

}

#if canImport(UIKit)
@available(iOS 11.0, tvOS 11.0, *)
@available(watchOS, unavailable)
extension UIKit.UIColor {

    private convenience init?(thinnableResource: ColorResource?) {
#if !os(watchOS)
        if let resource = thinnableResource {
            self.init(resource: resource)
        } else {
            return nil
        }
#else
        return nil
#endif
    }

}
#endif

#if canImport(SwiftUI)
@available(iOS 13.0, macOS 10.15, tvOS 13.0, watchOS 6.0, *)
extension SwiftUI.Color {

    private init?(thinnableResource: ColorResource?) {
        if let resource = thinnableResource {
            self.init(resource)
        } else {
            return nil
        }
    }

}

@available(iOS 13.0, macOS 10.15, tvOS 13.0, watchOS 6.0, *)
extension SwiftUI.ShapeStyle where Self == SwiftUI.Color {

    private init?(thinnableResource: ColorResource?) {
        if let resource = thinnableResource {
            self.init(resource)
        } else {
            return nil
        }
    }

}
#endif

@available(iOS 11.0, macOS 10.7, tvOS 11.0, *)
@available(watchOS, unavailable)
extension ImageResource {

    private init?(thinnableName: Swift.String, bundle: Foundation.Bundle) {
#if canImport(AppKit) && os(macOS)
        if bundle.image(forResource: NSImage.Name(thinnableName)) != nil {
            self.init(name: thinnableName, bundle: bundle)
        } else {
            return nil
        }
#elseif canImport(UIKit) && !os(watchOS)
        if UIKit.UIImage(named: thinnableName, in: bundle, compatibleWith: nil) != nil {
            self.init(name: thinnableName, bundle: bundle)
        } else {
            return nil
        }
#else
        return nil
#endif
    }

}

#if canImport(AppKit)
@available(macOS 10.7, *)
@available(macCatalyst, unavailable)
extension AppKit.NSImage {

    private convenience init?(thinnableResource: ImageResource?) {
#if !targetEnvironment(macCatalyst)
        if let resource = thinnableResource {
            self.init(resource: resource)
        } else {
            return nil
        }
#else
        return nil
#endif
    }

}
#endif

#if canImport(UIKit)
@available(iOS 11.0, tvOS 11.0, *)
@available(watchOS, unavailable)
extension UIKit.UIImage {

    private convenience init?(thinnableResource: ImageResource?) {
#if !os(watchOS)
        if let resource = thinnableResource {
            self.init(resource: resource)
        } else {
            return nil
        }
#else
        return nil
#endif
    }

}
#endif

// MARK: - Backwards Deployment Support -

/// A color resource.
struct ColorResource: Swift.Hashable, Swift.Sendable {

    /// An asset catalog color resource name.
    fileprivate let name: Swift.String

    /// An asset catalog color resource bundle.
    fileprivate let bundle: Foundation.Bundle

    /// Initialize a `ColorResource` with `name` and `bundle`.
    init(name: Swift.String, bundle: Foundation.Bundle) {
        self.name = name
        self.bundle = bundle
    }

}

/// An image resource.
struct ImageResource: Swift.Hashable, Swift.Sendable {

    /// An asset catalog image resource name.
    fileprivate let name: Swift.String

    /// An asset catalog image resource bundle.
    fileprivate let bundle: Foundation.Bundle

    /// Initialize an `ImageResource` with `name` and `bundle`.
    init(name: Swift.String, bundle: Foundation.Bundle) {
        self.name = name
        self.bundle = bundle
    }

}

#if canImport(AppKit)
@available(macOS 10.13, *)
@available(macCatalyst, unavailable)
extension AppKit.NSColor {

    /// Initialize a `NSColor` with a color resource.
    convenience init(resource: ColorResource) {
        self.init(named: NSColor.Name(resource.name), bundle: resource.bundle)!
    }

}

protocol _ACResourceInitProtocol {}
extension AppKit.NSImage: _ACResourceInitProtocol {}

@available(macOS 10.7, *)
@available(macCatalyst, unavailable)
extension _ACResourceInitProtocol {

    /// Initialize a `NSImage` with an image resource.
    init(resource: ImageResource) {
        self = resource.bundle.image(forResource: NSImage.Name(resource.name))! as! Self
    }

}
#endif

#if canImport(UIKit)
@available(iOS 11.0, tvOS 11.0, *)
@available(watchOS, unavailable)
extension UIKit.UIColor {

    /// Initialize a `UIColor` with a color resource.
    convenience init(resource: ColorResource) {
#if !os(watchOS)
        self.init(named: resource.name, in: resource.bundle, compatibleWith: nil)!
#else
        self.init()
#endif
    }

}

@available(iOS 11.0, tvOS 11.0, *)
@available(watchOS, unavailable)
extension UIKit.UIImage {

    /// Initialize a `UIImage` with an image resource.
    convenience init(resource: ImageResource) {
#if !os(watchOS)
        self.init(named: resource.name, in: resource.bundle, compatibleWith: nil)!
#else
        self.init()
#endif
    }

}
#endif

#if canImport(SwiftUI)
@available(iOS 13.0, macOS 10.15, tvOS 13.0, watchOS 6.0, *)
extension SwiftUI.Color {

    /// Initialize a `Color` with a color resource.
    init(_ resource: ColorResource) {
        self.init(resource.name, bundle: resource.bundle)
    }

}

@available(iOS 13.0, macOS 10.15, tvOS 13.0, watchOS 6.0, *)
extension SwiftUI.Image {

    /// Initialize an `Image` with an image resource.
    init(_ resource: ImageResource) {
        self.init(resource.name, bundle: resource.bundle)
    }

}
#endif