//
//  CoreDataExtensions.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/30.
//

import Foundation
import CoreData

// MARK: - User Extensions
extension User {
    
    /// 获取用户的所有成员
    var allMembers: [Member] {
        let set = members as? Set<Member> ?? []
        return Array(set).sorted { $0.memberNumber < $1.memberNumber }
    }
    
    /// 获取用户的所有全局规则
    var allGlobalRules: [GlobalRule] {
        let set = globalRules as? Set<GlobalRule> ?? []
        return Array(set).sorted {
            let createdAt1 = $0.createdAt ?? Date.distantPast
            let createdAt2 = $1.createdAt ?? Date.distantPast
            return createdAt1 < createdAt2
        }
    }
    
    /// 获取常用的全局规则
    var frequentGlobalRules: [GlobalRule] {
        return allGlobalRules.filter { $0.isFrequent }
    }
    
    /// 计算全家总积分
    var totalFamilyPoints: Int32 {
        return allMembers.reduce(0) { $0 + $1.currentPoints }
    }
    
    /// 获取当前订阅状态
    var isSubscriptionActive: Bool {
        return subscription?.isActive ?? false
    }
    
    /// 获取订阅类型
    var subscriptionType: String {
        return subscription?.subscriptionType ?? "free"
    }
    
    /// 检查是否为高级会员
    var isPremiumMember: Bool {
        return subscriptionType == "premium" && isSubscriptionActive
    }
    
    /// 检查是否为初级会员或以上
    var isBasicMemberOrAbove: Bool {
        let type = subscriptionType
        return (type == "basic" || type == "premium") && isSubscriptionActive
    }
}

// MARK: - Member Extensions
extension Member {
    
    /// 计算年龄
    var age: Int {
        guard let birthDate = birthDate else { return 0 }
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.year], from: birthDate, to: Date())
        return ageComponents.year ?? 0
    }
    
    /// 获取所有积分记录（按时间倒序）
    var allPointRecords: [PointRecord] {
        let set = pointRecords as? Set<PointRecord> ?? []
        return Array(set).sorted {
            let timestamp1 = $0.timestamp ?? Date.distantPast
            let timestamp2 = $1.timestamp ?? Date.distantPast
            return timestamp1 > timestamp2
        }
    }

    /// 获取行为相关的积分记录（排除兑换和抽奖）
    var behaviorPointRecords: [PointRecord] {
        return allPointRecords.filter {
            ($0.recordType ?? "behavior") == "behavior" && !($0.isReversed)
        }
    }

    /// 获取所有日记条目（按时间倒序）
    var allDiaryEntries: [DiaryEntry] {
        let set = diaryEntries as? Set<DiaryEntry> ?? []
        return Array(set).sorted {
            let timestamp1 = $0.timestamp ?? Date.distantPast
            let timestamp2 = $1.timestamp ?? Date.distantPast
            return timestamp1 > timestamp2
        }
    }

    /// 获取所有AI报告（按创建时间倒序）
    var allAIReports: [AIReport] {
        let set = aiReports as? Set<AIReport> ?? []
        return Array(set).sorted {
            let createdAt1 = $0.createdAt ?? Date.distantPast
            let createdAt2 = $1.createdAt ?? Date.distantPast
            return createdAt1 > createdAt2
        }
    }

    /// 获取成员规则
    var allMemberRules: [MemberRule] {
        let set = memberRules as? Set<MemberRule> ?? []
        return Array(set).sorted {
            let createdAt1 = $0.createdAt ?? Date.distantPast
            let createdAt2 = $1.createdAt ?? Date.distantPast
            return createdAt1 < createdAt2
        }
    }

    /// 获取常用成员规则
    var frequentMemberRules: [MemberRule] {
        return allMemberRules.filter { $0.isFrequent }
    }

    /// 获取成员奖品
    var allMemberPrizes: [MemberPrize] {
        let set = memberPrizes as? Set<MemberPrize> ?? []
        return Array(set).sorted {
            let createdAt1 = $0.createdAt ?? Date.distantPast
            let createdAt2 = $1.createdAt ?? Date.distantPast
            return createdAt1 < createdAt2
        }
    }

    /// 获取兑换记录（按时间倒序）
    var allRedemptionRecords: [RedemptionRecord] {
        let set = redemptionRecords as? Set<RedemptionRecord> ?? []
        return Array(set).sorted {
            let timestamp1 = $0.timestamp ?? Date.distantPast
            let timestamp2 = $1.timestamp ?? Date.distantPast
            return timestamp1 > timestamp2
        }
    }

    /// 获取抽奖记录（按时间倒序）
    var allLotteryRecords: [LotteryRecord] {
        let set = lotteryRecords as? Set<LotteryRecord> ?? []
        return Array(set).sorted {
            let timestamp1 = $0.timestamp ?? Date.distantPast
            let timestamp2 = $1.timestamp ?? Date.distantPast
            return timestamp1 > timestamp2
        }
    }
    
    /// 检查是否可以生成AI分析报告
    var canGenerateAnalysisReport: Bool {
        return behaviorPointRecords.count >= 10
    }
    
    /// 检查是否可以生成成长报告
    var canGenerateGrowthReport: Bool {
        return allDiaryEntries.count >= 10
    }
    
    /// 检查是否为孩子角色
    var isChild: Bool {
        let memberRole = role ?? ""
        return memberRole == "son" || memberRole == "daughter"
    }

    /// 获取显示名称
    var displayName: String {
        return name ?? "未知成员"
    }

    /// 获取角色显示名称
    var roleDisplayName: String {
        switch role ?? "" {
        case "father":
            return "爸爸"
        case "mother":
            return "妈妈"
        case "son":
            return "儿子"
        case "daughter":
            return "女儿"
        default:
            return "其他"
        }
    }

    /// 获取头像图片名称
    var avatarImageName: String {
        switch role ?? "" {
        case "father":
            return "person.fill"
        case "mother":
            return "person.fill"
        case "son":
            return "person.fill"
        case "daughter":
            return "person.fill"
        default:
            return "person.fill"
        }
    }

    /// 获取系统头像名称
    var systemAvatarName: String {
        switch role ?? "" {
        case "father":
            return "person.fill"
        case "mother":
            return "person.fill"
        case "son":
            return "person.fill"
        case "daughter":
            return "person.fill"
        default:
            return "person.circle.fill"
        }
    }

    // MARK: - Lottery Related Methods

    /// 获取所有抽奖配置
    var allLotteryConfigs: [LotteryConfig] {
        let set = lotteryConfigs as? Set<LotteryConfig> ?? []
        return Array(set).sorted {
            let createdAt1 = $0.createdAt ?? Date.distantPast
            let createdAt2 = $1.createdAt ?? Date.distantPast
            return createdAt1 < createdAt2
        }
    }

    /// 获取指定类型的抽奖配置
    func getLotteryConfig(for toolType: LotteryConfig.ToolType) -> LotteryConfig? {
        return allLotteryConfigs.first { $0.lotteryToolType == toolType }
    }

    /// 检查是否有大转盘配置
    var hasWheelConfig: Bool {
        return getLotteryConfig(for: .wheel) != nil
    }

    /// 检查是否可以进行抽奖（积分是否足够）
    func canAffordLottery(for toolType: LotteryConfig.ToolType) -> Bool {
        guard let config = getLotteryConfig(for: toolType) else { return false }
        return currentPoints >= config.costPerPlay
    }

    /// 创建或更新抽奖配置
    @discardableResult
    func createOrUpdateLotteryConfig(
        toolType: LotteryConfig.ToolType,
        itemCount: Int,
        costPerPlay: Int,
        prizeNames: [String],
        in context: NSManagedObjectContext
    ) -> LotteryConfig {
        // 检查是否已存在配置
        if let existingConfig = getLotteryConfig(for: toolType) {
            // 更新现有配置
            existingConfig.updateConfig(itemCount: itemCount, costPerPlay: costPerPlay)

            // 清空现有项目
            existingConfig.clearAllItems(in: context)

            // 添加新项目
            for (index, prizeName) in prizeNames.enumerated() {
                existingConfig.addItem(index: index, prizeName: prizeName, in: context)
            }

            return existingConfig
        } else {
            // 创建新配置
            let config = LotteryConfig.create(
                toolType: toolType,
                itemCount: itemCount,
                costPerPlay: costPerPlay,
                for: self,
                in: context
            )

            // 添加项目
            for (index, prizeName) in prizeNames.enumerated() {
                config.addItem(index: index, prizeName: prizeName, in: context)
            }

            return config
        }
    }
}

// MARK: - PointRecord Extensions
extension PointRecord {
    
    /// 获取记录类型显示名称
    var recordTypeDisplayName: String {
        switch recordType ?? "behavior" {
        case "behavior":
            return "行为记录"
        case "redemption":
            return "奖品兑换"
        case "lottery":
            return "抽奖消耗"
        default:
            return "未知类型"
        }
    }

    /// 获取积分变化描述
    var pointChangeDescription: String {
        let pointValue = value
        let sign = pointValue >= 0 ? "+" : ""
        return "\(sign)\(pointValue)"
    }
}

// MARK: - AIReport Extensions
extension AIReport {
    
    /// 获取报告类型显示名称
    var reportTypeDisplayName: String {
        switch reportType ?? "analysis" {
        case "analysis":
            return "行为分析报告"
        case "growth":
            return "成长分析报告"
        default:
            return "未知报告"
        }
    }

    /// 获取格式化的创建时间
    var formattedCreatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: createdAt ?? Date())
    }
}

// MARK: - Subscription Extensions
extension Subscription {
    
    /// 获取订阅类型显示名称
    var subscriptionTypeDisplayName: String {
        switch subscriptionType ?? "free" {
        case "free":
            return "免费版"
        case "basic":
            return "初级会员"
        case "premium":
            return "高级会员"
        default:
            return "未知类型"
        }
    }
    
    /// 检查订阅是否即将过期（7天内）
    var isExpiringSoon: Bool {
        guard let endDate = endDate else { return false }
        let sevenDaysFromNow = Calendar.current.date(byAdding: .day, value: 7, to: Date()) ?? Date()
        return endDate <= sevenDaysFromNow
    }
    
    /// 获取剩余天数
    var remainingDays: Int {
        guard let endDate = endDate else { return 0 }
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: Date(), to: endDate)
        return max(0, components.day ?? 0)
    }
}

// MARK: - LotteryConfig Extensions
extension LotteryConfig {

    // MARK: - Enums

    enum ToolType: String, CaseIterable {
        case wheel = "wheel"
        case blindbox = "blindbox"
        case scratchcard = "scratchcard"

        var displayName: String {
            switch self {
            case .wheel:
                return "大转盘"
            case .blindbox:
                return "盲盒"
            case .scratchcard:
                return "刮刮卡"
            }
        }

        var defaultItemCount: Int {
            switch self {
            case .wheel:
                return 8   // 大转盘默认8个分区
            case .blindbox, .scratchcard:
                return 5   // 盲盒和刮刮卡默认5个
            }
        }

        var maxItemCount: Int {
            switch self {
            case .wheel:
                return 12  // 大转盘最多12个分区
            case .blindbox, .scratchcard:
                return 20  // 盲盒和刮刮卡最多20个
            }
        }

        var minItemCount: Int {
            switch self {
            case .wheel:
                return 4   // 大转盘最少4个分区
            case .blindbox, .scratchcard:
                return 2   // 盲盒和刮刮卡最少2个
            }
        }

        var itemNamePrefix: String {
            switch self {
            case .wheel:
                return "分区"
            case .blindbox:
                return "盲盒"
            case .scratchcard:
                return "刮刮卡"
            }
        }

        var configTitle: String {
            switch self {
            case .wheel:
                return "大转盘配置"
            case .blindbox:
                return "盲盒配置"
            case .scratchcard:
                return "刮刮卡配置"
            }
        }
    }

    // MARK: - Computed Properties

    /// 获取抽奖道具类型枚举
    var lotteryToolType: ToolType {
        return ToolType(rawValue: toolType ?? "wheel") ?? .wheel
    }

    /// 获取抽奖道具类型显示名称
    var toolTypeDisplayName: String {
        return lotteryToolType.displayName
    }

    /// 获取所有抽奖项目（按索引排序）
    var allItems: [LotteryItem] {
        let set = items as? Set<LotteryItem> ?? []
        return Array(set).sorted { $0.itemIndex < $1.itemIndex }
    }

    /// 格式化显示时间
    var formattedCreatedTime: String {
        guard let createdAt = createdAt else { return "" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-M-d HH:mm"
        return formatter.string(from: createdAt)
    }

    /// 检查配置是否完整
    var isComplete: Bool {
        let currentItemCount = items?.count ?? 0
        return currentItemCount == itemCount && currentItemCount > 0
    }

    /// 检查配置是否有效
    var isValid: Bool {
        let toolType = lotteryToolType
        let currentItemCount = Int(itemCount)
        return currentItemCount >= toolType.minItemCount &&
               currentItemCount <= toolType.maxItemCount &&
               costPerPlay >= 0
    }

    // MARK: - Convenience Methods

    /// 创建新抽奖配置的便利方法
    @discardableResult
    static func create(
        toolType: ToolType,
        itemCount: Int,
        costPerPlay: Int,
        for member: Member,
        in context: NSManagedObjectContext
    ) -> LotteryConfig {
        let config = LotteryConfig(context: context)
        config.id = UUID()
        config.toolType = toolType.rawValue
        config.itemCount = Int32(itemCount)
        config.costPerPlay = Int32(costPerPlay)
        config.createdAt = Date()
        config.updatedAt = Date()
        config.member = member
        return config
    }

    /// 更新配置信息
    func updateConfig(itemCount: Int, costPerPlay: Int) {
        self.itemCount = Int32(itemCount)
        self.costPerPlay = Int32(costPerPlay)
        self.updatedAt = Date()
    }

    /// 添加道具项目
    @discardableResult
    func addItem(index: Int, prizeName: String, in context: NSManagedObjectContext) -> LotteryItem {
        let item = LotteryItem.create(
            index: index,
            prizeName: prizeName,
            for: self,
            in: context
        )
        return item
    }

    /// 移除指定索引的道具项目
    func removeItem(at index: Int, in context: NSManagedObjectContext) {
        guard let itemToRemove = allItems.first(where: { $0.itemIndex == index }) else { return }
        context.delete(itemToRemove)
        self.updatedAt = Date()
    }

    /// 清空所有道具项目
    func clearAllItems(in context: NSManagedObjectContext) {
        allItems.forEach { context.delete($0) }
        self.updatedAt = Date()
    }
}

// MARK: - LotteryItem Extensions
extension LotteryItem {

    // MARK: - Computed Properties

    /// 获取格式化的奖品名称（处理空值）
    var formattedPrizeName: String {
        return prizeName?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
    }

    /// 获取显示用的项目标题
    var displayTitle: String {
        let toolType = lotteryConfig?.lotteryToolType ?? .wheel
        switch toolType {
        case .wheel:
            return "分区 \(itemIndex + 1)"
        case .blindbox:
            return "盲盒 \(itemIndex + 1)"
        case .scratchcard:
            return "刮刮卡 \(itemIndex + 1)"
        }
    }

    /// 格式化显示时间
    var formattedCreatedTime: String {
        guard let createdAt = createdAt else { return "" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-M-d HH:mm"
        return formatter.string(from: createdAt)
    }

    /// 检查奖品名称是否有效
    var hasValidPrizeName: Bool {
        return !formattedPrizeName.isEmpty && formattedPrizeName.count <= 20
    }

    /// 获取所属道具类型的显示名称
    var toolTypeDisplayName: String {
        return lotteryConfig?.toolTypeDisplayName ?? ""
    }

    // MARK: - Convenience Methods

    /// 创建新抽奖道具项目的便利方法
    @discardableResult
    static func create(
        index: Int,
        prizeName: String,
        for lotteryConfig: LotteryConfig,
        in context: NSManagedObjectContext
    ) -> LotteryItem {
        let item = LotteryItem(context: context)
        item.id = UUID()
        item.itemIndex = Int32(index)
        item.prizeName = prizeName
        item.createdAt = Date()
        item.lotteryConfig = lotteryConfig
        return item
    }

    /// 更新奖品名称
    func updatePrizeName(_ name: String) {
        self.prizeName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        self.lotteryConfig?.updatedAt = Date()
    }

    /// 检查与同一配置下其他项目的重复性
    func checkDuplicatePrizeName() -> (isDuplicate: Bool, duplicateIndex: Int?) {
        guard let config = lotteryConfig else { return (false, nil) }

        let otherItems = config.allItems.filter { $0.itemIndex != self.itemIndex }
        let duplicateItem = otherItems.first { item in
            item.formattedPrizeName.lowercased() == self.formattedPrizeName.lowercased()
        }

        if let duplicate = duplicateItem {
            return (true, Int(duplicate.itemIndex))
        }

        return (false, nil)
    }

    /// 获取项目在配置中的位置信息
    func getPositionInfo() -> (current: Int, total: Int) {
        guard let config = lotteryConfig else { return (0, 0) }
        return (Int(itemIndex), Int(config.itemCount))
    }
}
