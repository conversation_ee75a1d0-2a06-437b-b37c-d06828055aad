//
//  OverlayTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * Overlay弹窗测试视图
 * 用于测试从sheet改为overlay后的弹窗效果
 */
struct OverlayTestView: View {
    
    @State private var showOverlayPopup = false
    @State private var showSheetPopup = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                
                Text("弹窗显示方式对比测试")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.top, 50)
                
                VStack(spacing: 20) {
                    
                    // Overlay弹窗测试按钮
                    Button(action: {
                        showOverlayPopup = true
                    }) {
                        HStack {
                            Image(systemName: "rectangle.stack")
                                .foregroundColor(.white)
                            Text("显示Overlay弹窗")
                                .foregroundColor(.white)
                                .fontWeight(.medium)
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [Color.blue, Color.purple]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(12)
                    }
                    
                    // Sheet弹窗测试按钮
                    But<PERSON>(action: {
                        showSheetPopup = true
                    }) {
                        HStack {
                            Image(systemName: "doc.plaintext")
                                .foregroundColor(.white)
                            Text("显示Sheet弹窗")
                                .foregroundColor(.white)
                                .fontWeight(.medium)
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [Color.green, Color.teal]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(12)
                    }
                }
                .padding(.horizontal, 30)
                
                Spacer()
                
                VStack(spacing: 10) {
                    Text("测试说明:")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("• Overlay弹窗：显示在当前视图上方")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("• Sheet弹窗：从底部滑出的模态视图")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.bottom, 50)
            }
            .navigationTitle("弹窗测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        // Overlay弹窗
        .overlay(
            Group {
                if showOverlayPopup {
                    TestOverlayPopup(isPresented: $showOverlayPopup)
                }
            }
        )
        // Sheet弹窗
        .sheet(isPresented: $showSheetPopup) {
            TestSheetPopup(isPresented: $showSheetPopup)
        }
    }
}

/**
 * 测试用的Overlay弹窗
 */
struct TestOverlayPopup: View {
    
    @Binding var isPresented: Bool
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.4)
                .ignoresSafeArea(.all)
                .onTapGesture {
                    isPresented = false
                }
            
            // 弹窗内容
            VStack(spacing: 20) {
                
                HStack {
                    Text("Overlay弹窗")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Spacer()
                    
                    Button(action: {
                        isPresented = false
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(.gray)
                    }
                }
                
                Text("这是一个使用overlay方式显示的弹窗")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Text("特点：显示在当前视图的上方，不会改变导航栈")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Button(action: {
                    isPresented = false
                }) {
                    Text("关闭")
                        .foregroundColor(.white)
                        .fontWeight(.medium)
                        .frame(maxWidth: .infinity)
                        .frame(height: 44)
                        .background(Color.blue)
                        .cornerRadius(8)
                }
            }
            .padding(20)
            .background(Color(.systemBackground))
            .cornerRadius(16)
            .shadow(radius: 10)
            .padding(.horizontal, 30)
        }
        .transition(.opacity.combined(with: .scale(scale: 0.95)))
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isPresented)
    }
}

/**
 * 测试用的Sheet弹窗
 */
struct TestSheetPopup: View {
    
    @Binding var isPresented: Bool
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                
                Text("Sheet弹窗")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.top, 20)
                
                Text("这是一个使用sheet方式显示的弹窗")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Text("特点：从底部滑出，是一个独立的模态视图")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Spacer()
                
                Button(action: {
                    isPresented = false
                }) {
                    Text("关闭")
                        .foregroundColor(.white)
                        .fontWeight(.medium)
                        .frame(maxWidth: .infinity)
                        .frame(height: 44)
                        .background(Color.green)
                        .cornerRadius(8)
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
            }
            .navigationTitle("Sheet弹窗")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("完成") {
                    isPresented = false
                }
            )
        }
    }
}

#Preview {
    OverlayTestView()
}
