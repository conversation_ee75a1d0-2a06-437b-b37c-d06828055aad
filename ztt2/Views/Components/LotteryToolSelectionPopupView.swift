//
//  LotteryToolSelectionPopupView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 抽奖道具选择弹窗组件
 * 用于选择要配置的抽奖道具类型（大转盘、盲盒、刮刮卡）
 */
struct LotteryToolSelectionPopupView: View {

    @Binding var isPresented: Bool
    let selectedMember: Member?
    let onToolSelected: (Member, LotteryConfig.ToolType) -> Void

    @State private var animationTrigger = false
    @State private var showConfigPopup = false
    @State private var selectedToolType: LotteryConfig.ToolType = .wheel
    
    // 抽奖道具类型数据
    private let lotteryTools: [LotteryToolData] = [
        LotteryToolData(
            type: .wheel,
            icon: "circle.grid.cross.fill",
            color: "#FF9500",
            description: "转动转盘，随机获得奖品"
        ),
        LotteryToolData(
            type: .blindbox,
            icon: "shippingbox.fill",
            color: "#007AFF",
            description: "打开盲盒，惊喜等你发现"
        ),
        LotteryToolData(
            type: .scratchcard,
            icon: "rectangle.fill",
            color: "#FF2D92",
            description: "刮开卡片，揭晓神秘奖品"
        )
    ]
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)
                
                // 弹窗内容
                VStack(spacing: 0) {
                    // 标题栏
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("选择抽奖道具")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                            
                            if let member = selectedMember {
                                Text("为 \(member.name ?? "未知成员") 配置")
                                    .font(.system(size: 14))
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                            }
                        }
                        
                        Spacer()
                        
                        Button(action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                isPresented = false
                            }
                        }) {
                            Image(systemName: "xmark")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                    .background(Color.white)
                    
                    // 分割线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                    
                    // 道具列表
                    VStack(spacing: 0) {
                        ForEach(lotteryTools, id: \.type) { tool in
                            LotteryToolSelectionRow(
                                toolData: tool,
                                action: {
                                    guard selectedMember != nil else { return }
                                    selectedToolType = tool.type
                                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                        isPresented = false
                                    }
                                    // 延迟显示配置弹窗，确保选择弹窗完全关闭
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                        showConfigPopup = true
                                    }
                                }
                            )
                            
                            if tool.type != lotteryTools.last?.type {
                                Rectangle()
                                    .fill(Color(hex: "#edf5d9"))
                                    .frame(height: 1)
                                    .padding(.horizontal, 20)
                            }
                        }
                    }
                    .background(Color.white)
                }
                .background(Color.white)
                .cornerRadius(16)
                .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
                .padding(.horizontal, 40)
                .scaleEffect(animationTrigger ? 1.0 : 0.8)
                .opacity(animationTrigger ? 1.0 : 0.0)
                .onAppear {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                        animationTrigger = true
                    }
                }
                .onDisappear {
                    animationTrigger = false
                }
            }
        }
        .lotteryConfigOverlay(
            isPresented: $showConfigPopup,
            member: selectedMember,
            toolType: selectedToolType,
            onSave: { formData in
                // 这里可以处理保存逻辑
                print("保存配置: \(formData.toConfigData())")
                if let member = selectedMember {
                    onToolSelected(member, selectedToolType)
                }
            },
            onCancel: {
                print("取消配置")
            }
        )
    }
}

/**
 * 抽奖道具选择行组件
 */
struct LotteryToolSelectionRow: View {
    
    let toolData: LotteryToolData
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // 道具图标
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color(hex: toolData.color).opacity(0.8),
                                    Color(hex: toolData.color)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 50, height: 50)
                    
                    Image(systemName: toolData.icon)
                        .font(.system(size: 22, weight: .medium))
                        .foregroundColor(.white)
                }
                
                // 道具信息
                VStack(alignment: .leading, spacing: 6) {
                    Text(toolData.type.displayName)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text(toolData.description)
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .lineLimit(2)
                }
                
                Spacer()
                
                // 箭头图标
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 18)
            .background(
                Color.white
                    .overlay(
                        isPressed ? Color.black.opacity(0.05) : Color.clear
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

/**
 * 抽奖道具数据模型
 */
struct LotteryToolData {
    let type: LotteryConfig.ToolType
    let icon: String
    let color: String
    let description: String
}

#Preview {
    LotteryToolSelectionPopupView(
        isPresented: .constant(true),
        selectedMember: nil,
        onToolSelected: { _, _ in }
    )
}
