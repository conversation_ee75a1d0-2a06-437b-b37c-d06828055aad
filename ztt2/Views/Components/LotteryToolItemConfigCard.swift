//
//  LotteryToolItemConfigCard.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 抽奖道具项目配置卡片组件
 * 用于配置单个抽奖项目的奖品名称
 */
struct LotteryToolItemConfigCard: View {
    
    @ObservedObject var item: LotteryToolItemData
    let toolColor: String
    let onPrizeNameChanged: (String) -> Void
    
    @State private var isEditing = false
    @FocusState private var isTextFieldFocused: Bool
    
    var body: some View {
        VStack(spacing: 0) {
            // 项目标题行
            HStack(spacing: 12) {
                // 项目编号图标
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color(hex: toolColor).opacity(0.8),
                                    Color(hex: toolColor)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 32, height: 32)
                    
                    Text("\(item.index)")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.white)
                }
                
                // 项目标题
                VStack(alignment: .leading, spacing: 2) {
                    Text(item.displayTitle)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text("设置奖品名称")
                        .font(.system(size: 12))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
                
                // 编辑状态指示器
                if isEditing {
                    Image(systemName: "pencil.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(Color(hex: toolColor))
                }
            }
            .padding(.horizontal, 16)
            .padding(.top, 16)
            .padding(.bottom, 12)
            
            // 奖品名称输入区域
            VStack(spacing: 8) {
                TextField(item.placeholderText, text: Binding(
                    get: { item.prizeName },
                    set: { newValue in
                        item.prizeName = newValue
                        onPrizeNameChanged(newValue)
                    }
                ))
                .textFieldStyle(CustomTextFieldStyle(
                    isEditing: isEditing,
                    toolColor: toolColor
                ))
                .font(.system(size: 16))
                .focused($isTextFieldFocused)
                .onTapGesture {
                    isTextFieldFocused = true
                }
                .onChange(of: isTextFieldFocused) { focused in
                    withAnimation(.easeInOut(duration: 0.2)) {
                        isEditing = focused
                    }
                }
                
                // 输入提示
                if isEditing {
                    HStack {
                        Image(systemName: "lightbulb.fill")
                            .font(.system(size: 12))
                            .foregroundColor(Color(hex: toolColor).opacity(0.7))
                        
                        Text("lottery_config.item.suggestion_tip".localized)
                            .font(.system(size: 12))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        
                        Spacer()
                    }
                    .transition(.opacity.combined(with: .move(edge: .top)))
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(
                            isEditing ? Color(hex: toolColor).opacity(0.5) : Color(hex: "#edf5d9"),
                            lineWidth: isEditing ? 2 : 1
                        )
                )
        )
        .shadow(
            color: isEditing ? Color(hex: toolColor).opacity(0.1) : Color.clear,
            radius: isEditing ? 8 : 0,
            x: 0,
            y: isEditing ? 4 : 0
        )
        .scaleEffect(isEditing ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isEditing)
    }
}

/**
 * 自定义文本输入框样式
 */
struct CustomTextFieldStyle: TextFieldStyle {
    let isEditing: Bool
    let toolColor: String
    
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isEditing ? Color.white : Color(hex: "#f8f9fa"))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(
                                isEditing ? Color(hex: toolColor).opacity(0.3) : Color(hex: "#e9ecef"),
                                lineWidth: 1
                            )
                    )
            )
    }
}

/**
 * 奖品名称快速选择组件（可选功能）
 */
struct PrizeNameSuggestions: View {
    let toolType: LotteryConfig.ToolType
    let onSuggestionSelected: (String) -> Void
    
    private var suggestions: [String] {
        switch toolType {
        case .wheel:
            return ["小红花", "贴纸", "铅笔", "橡皮", "糖果", "小玩具", "表扬信", "免作业券"]
        case .blindbox:
            return ["神秘礼品", "惊喜奖品", "特别奖励", "幸运奖", "安慰奖", "大奖", "小礼物", "纪念品"]
        case .scratchcard:
            return ["一等奖", "二等奖", "三等奖", "幸运奖", "参与奖", "特别奖", "惊喜奖", "纪念奖"]
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("快速选择")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 8) {
                ForEach(suggestions, id: \.self) { suggestion in
                    Button(action: {
                        onSuggestionSelected(suggestion)
                    }) {
                        Text(suggestion)
                            .font(.system(size: 12))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color(hex: "#f8f9fa"))
                            .cornerRadius(6)
                    }
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 12)
    }
}

/**
 * 带建议的抽奖道具项目配置卡片
 */
struct LotteryToolItemConfigCardWithSuggestions: View {
    
    @ObservedObject var item: LotteryToolItemData
    let toolColor: String
    let onPrizeNameChanged: (String) -> Void
    
    @State private var showSuggestions = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 基础配置卡片
            LotteryToolItemConfigCard(
                item: item,
                toolColor: toolColor,
                onPrizeNameChanged: onPrizeNameChanged
            )
            
            // 建议选项（可展开）
            if showSuggestions {
                PrizeNameSuggestions(
                    toolType: item.toolType,
                    onSuggestionSelected: { suggestion in
                        item.prizeName = suggestion
                        onPrizeNameChanged(suggestion)
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showSuggestions = false
                        }
                    }
                )
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
            
            // 建议按钮
            if item.prizeName.isEmpty {
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showSuggestions.toggle()
                    }
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: showSuggestions ? "chevron.up" : "lightbulb")
                            .font(.system(size: 12))
                        
                        Text(showSuggestions ? "lottery_config.item.hide_suggestions".localized : "lottery_config.item.suggestions".localized)
                            .font(.system(size: 12))
                    }
                    .foregroundColor(Color(hex: toolColor))
                    .padding(.vertical, 8)
                }
                .transition(.opacity)
            }
        }
    }
}

#Preview {
    VStack(spacing: 16) {
        LotteryToolItemConfigCard(
            item: LotteryToolItemData(index: 1, prizeName: "", toolType: .wheel),
            toolColor: "#FF9500",
            onPrizeNameChanged: { _ in }
        )
        
        LotteryToolItemConfigCard(
            item: LotteryToolItemData(index: 2, prizeName: "小红花", toolType: .wheel),
            toolColor: "#FF9500",
            onPrizeNameChanged: { _ in }
        )
        
        LotteryToolItemConfigCardWithSuggestions(
            item: LotteryToolItemData(index: 3, prizeName: "", toolType: .blindbox),
            toolColor: "#007AFF",
            onPrizeNameChanged: { _ in }
        )
    }
    .padding()
    .background(Color(hex: "#f8f9fa"))
}
