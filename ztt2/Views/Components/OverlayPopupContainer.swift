//
//  OverlayPopupContainer.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * Overlay弹窗容器
 * 用于确保弹窗能够正确显示在最顶层，避免被其他视图遮挡
 */
struct OverlayPopupContainer<Content: View>: View {

    let isPresented: Bool
    let content: () -> Content

    init(isPresented: Bool, @ViewBuilder content: @escaping () -> Content) {
        self.isPresented = isPresented
        self.content = content
    }

    var body: some View {
        ZStack {
            if isPresented {
                content()
                    .zIndex(1000) // 确保在最顶层
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
            }
        }
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isPresented)
    }
}

// MARK: - View Extensions

extension View {

    /**
     * 显示抽奖配置弹窗 - 使用overlay方式
     */
    func lotteryConfigOverlay(
        isPresented: Binding<Bool>,
        member: Member?,
        toolType: LotteryConfig.ToolType,
        onSave: @escaping (LotteryToolFormData) -> Void,
        onCancel: @escaping () -> Void
    ) -> some View {
        self.overlay(
            Group {
                if isPresented.wrappedValue, let member = member {
                    LotteryToolConfigPopupView(
                        isPresented: isPresented,
                        selectedMember: member,
                        toolType: toolType,
                        onSave: onSave,
                        onCancel: onCancel
                    )
                    .zIndex(1000)
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
                }
            }
            .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isPresented.wrappedValue)
        )
    }

    /**
     * 显示抽奖道具选择弹窗 - 使用overlay方式
     */
    func lotteryToolSelectionOverlay(
        isPresented: Binding<Bool>,
        member: Member?,
        onToolSelected: @escaping (Member, LotteryConfig.ToolType) -> Void
    ) -> some View {
        self.overlay(
            Group {
                if isPresented.wrappedValue, let member = member {
                    LotteryToolSelectionPopupView(
                        isPresented: isPresented,
                        selectedMember: member,
                        onToolSelected: onToolSelected
                    )
                    .zIndex(1000)
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
                }
            }
            .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isPresented.wrappedValue)
        )
    }
}

#Preview {
    VStack {
        Text("测试内容")
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color.gray.opacity(0.1))
    }
    .lotteryToolSelectionOverlay(
        isPresented: .constant(true),
        member: nil,
        onToolSelected: { _, _ in }
    )
}
