//
//  LotteryToolConfigPopupView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 抽奖道具配置弹窗组件
 * 用于配置大转盘、盲盒、刮刮卡的详细参数
 */
struct LotteryToolConfigPopupView: View {
    
    @Binding var isPresented: Bool
    let selectedMember: Member
    let toolType: LotteryConfig.ToolType
    let onSave: (LotteryToolFormData) -> Void
    let onCancel: () -> Void
    
    @StateObject private var formData: LotteryToolFormData
    @State private var animationTrigger = false
    @State private var showValidationErrors = false
    
    // 初始化
    init(
        isPresented: Binding<Bool>,
        selectedMember: Member,
        toolType: LotteryConfig.ToolType,
        existingConfig: LotteryConfig? = nil,
        onSave: @escaping (LotteryToolFormData) -> Void,
        onCancel: @escaping () -> Void
    ) {
        self._isPresented = isPresented
        self.selectedMember = selectedMember
        self.toolType = toolType
        self.onSave = onSave
        self.onCancel = onCancel
        
        // 初始化表单数据
        if let config = existingConfig {
            self._formData = StateObject(wrappedValue: LotteryToolFormData(from: config))
        } else {
            self._formData = StateObject(wrappedValue: LotteryToolFormData(toolType: toolType))
        }
    }
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea(.all)
                    .onTapGesture {
                        dismissKeyboard()
                    }
                    .transition(.opacity)
                
                // 弹窗内容
                VStack(spacing: 0) {
                    // 标题栏
                    titleSection
                    
                    // 分割线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                    
                    // 表单内容
                    formContentSection
                    
                    // 分割线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                    
                    // 底部按钮
                    bottomButtonsSection
                }
                .background(Color.white)
                .cornerRadius(16)
                .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
                .padding(.horizontal, 20)
                .scaleEffect(animationTrigger ? 1.0 : 0.8)
                .opacity(animationTrigger ? 1.0 : 0.0)
                .onAppear {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                        animationTrigger = true
                    }
                }
                .onDisappear {
                    animationTrigger = false
                }
            }
        }
    }
    
    // MARK: - 标题栏
    private var titleSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(toolType.configTitle)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text("为 \(selectedMember.name ?? "member.info.unknown_name".localized) 配置")
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
            
            Button(action: {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    isPresented = false
                    onCancel()
                }
            }) {
                Image(systemName: "xmark")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(Color.white)
        .onTapGesture {
            dismissKeyboard()
        }
    }
    
    // MARK: - 表单内容
    private var formContentSection: some View {
        ScrollView(.vertical, showsIndicators: true) {
            VStack(spacing: 24) {
                // 成员信息卡片
                memberInfoCard
                
                // 基本配置
                basicConfigSection
                
                // 道具项目配置
                itemConfigSection
                
                // 验证错误显示
                if showValidationErrors && !formData.validationErrors.isEmpty {
                    validationErrorsSection
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 20)
        }
        .frame(maxHeight: UIScreen.main.bounds.height * 0.6)
        .onTapGesture {
            dismissKeyboard()
        }
    }
    
    // MARK: - 成员信息卡片
    private var memberInfoCard: some View {
        HStack(spacing: 12) {
            // 成员头像
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                DesignSystem.Colors.primary.opacity(0.8),
                                DesignSystem.Colors.primary
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 40, height: 40)
                
                Text(String(selectedMember.name?.prefix(1) ?? "?"))
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
            }
            
            // 成员信息
            VStack(alignment: .leading, spacing: 4) {
                Text(selectedMember.name ?? "未知成员")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("当前积分: \(selectedMember.currentPoints)")
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
            
            // 道具图标
            Image(systemName: getToolIcon())
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(Color(hex: getToolColor()))
        }
        .padding(16)
        .background(Color(hex: "#f8ffe5"))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1)
        )
    }
    
    // MARK: - 基本配置区域
    private var basicConfigSection: some View {
        VStack(spacing: 16) {
            SectionHeader(title: "lottery_config.form.basic_config".localized, icon: "gear")
            
            VStack(spacing: 16) {
                // 道具数量配置
                itemCountConfigRow
                
                // 积分消耗配置
                costPerPlayConfigRow
            }
        }
    }
    
    // MARK: - 道具数量配置行
    private var itemCountConfigRow: some View {
        HStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                Text("\(toolType.itemNamePrefix)数量")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)

                Text("范围: \(formData.minItemCount)-\(formData.maxItemCount)")
                    .font(.system(size: 12))
                    .foregroundColor(DesignSystem.Colors.textTertiary)
            }
            
            Spacer()
            
            // 数量调节器
            HStack(spacing: 12) {
                Button(action: {
                    if formData.itemCount > formData.minItemCount {
                        formData.setItemCount(formData.itemCount - 1)
                    }
                }) {
                    Image(systemName: "minus.circle.fill")
                        .font(.system(size: 24))
                        .foregroundColor(formData.itemCount > formData.minItemCount ? Color(hex: "#a9d051") : DesignSystem.Colors.textTertiary)
                }
                .disabled(formData.itemCount <= formData.minItemCount)
                
                Text("\(formData.itemCount)")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .frame(minWidth: 30)
                
                Button(action: {
                    if formData.itemCount < formData.maxItemCount {
                        formData.setItemCount(formData.itemCount + 1)
                    }
                }) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 24))
                        .foregroundColor(formData.itemCount < formData.maxItemCount ? Color(hex: "#a9d051") : DesignSystem.Colors.textTertiary)
                }
                .disabled(formData.itemCount >= formData.maxItemCount)
            }
        }
        .padding(16)
        .background(Color(hex: "#f8ffe5"))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1)
        )
    }
    
    // MARK: - 积分消耗配置行
    private var costPerPlayConfigRow: some View {
        HStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                Text("lottery_config.form.cost_per_play".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)

                Text("设置每次抽奖需要的积分")
                    .font(.system(size: 12))
                    .foregroundColor(DesignSystem.Colors.textTertiary)
            }
            
            Spacer()
            
            TextField("lottery_config.form.cost_placeholder".localized, value: $formData.costPerPlay, format: .number)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .font(.system(size: 16))
                .keyboardType(.numberPad)
                .frame(width: 80)
                .multilineTextAlignment(.center)
        }
        .padding(16)
        .background(Color(hex: "#f8ffe5"))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1)
        )
    }
    
    // MARK: - 道具项目配置区域
    private var itemConfigSection: some View {
        VStack(spacing: 16) {
            SectionHeader(title: "\(toolType.itemNamePrefix)奖品配置", icon: "list.bullet")
            
            LazyVStack(spacing: 12) {
                ForEach(Array(formData.items.enumerated()), id: \.element.id) { index, item in
                    LotteryToolItemConfigCard(
                        item: item,
                        toolColor: getToolColor(),
                        onPrizeNameChanged: { newName in
                            formData.updateItemPrizeName(at: index, name: newName)
                        }
                    )
                }
            }
        }
    }
    
    // MARK: - 验证错误区域
    private var validationErrorsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.errorColor)
                
                Text("请修正以下问题")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.errorColor)
            }
            
            ForEach(formData.validationErrors, id: \.self) { error in
                Text("• \(error)")
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.errorColor)
            }
        }
        .padding(16)
        .background(DesignSystem.Colors.errorColor.opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(DesignSystem.Colors.errorColor.opacity(0.3), lineWidth: 1)
        )
    }
    
    // MARK: - 底部按钮区域
    private var bottomButtonsSection: some View {
        HStack(spacing: 16) {
            // 取消按钮
            Button(action: {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    isPresented = false
                    onCancel()
                }
            }) {
                Text("lottery_config.form.cancel".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 48)
                    .background(Color(hex: "#f5f5f5"))
                    .cornerRadius(12)
            }
            
            // 保存按钮
            Button(action: {
                showValidationErrors = true
                if formData.isValid {
                    formData.isSaving = true
                    onSave(formData)
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                        isPresented = false
                    }
                }
            }) {
                HStack(spacing: 8) {
                    if formData.isSaving {
                        ProgressView()
                            .scaleEffect(0.8)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    }
                    
                    Text(formData.isSaving ? "lottery_config.form.saving".localized : "lottery_config.form.save".localized)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 48)
                .background(
                    LinearGradient(
                        colors: [Color(hex: "#a9d051"), Color(hex: "#8bb83f")],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(12)
            }
            .disabled(formData.isSaving)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(Color.white)
    }
    
    // MARK: - 辅助方法
    
    private func getToolIcon() -> String {
        switch toolType {
        case .wheel:
            return "circle.grid.cross.fill"
        case .blindbox:
            return "shippingbox.fill"
        case .scratchcard:
            return "rectangle.fill"
        }
    }
    
    private func getToolColor() -> String {
        switch toolType {
        case .wheel:
            return "#FF9500"
        case .blindbox:
            return "#007AFF"
        case .scratchcard:
            return "#FF2D92"
        }
    }
    
    private func dismissKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

/**
 * 区域标题组件
 */
struct SectionHeader: View {
    let title: String
    let icon: String
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color(hex: "#a9d051"))
            
            Text(title)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Spacer()
        }
    }
}

#Preview {
    LotteryToolConfigPopupView(
        isPresented: .constant(true),
        selectedMember: Member(),
        toolType: .wheel,
        onSave: { _ in },
        onCancel: { }
    )
}
