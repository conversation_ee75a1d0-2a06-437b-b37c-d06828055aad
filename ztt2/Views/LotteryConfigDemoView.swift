//
//  LotteryConfigDemoView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI
import CoreData

/**
 * 抽奖配置演示视图
 * 展示抽奖道具配置界面的完整功能
 */
struct LotteryConfigDemoView: View {
    
    @Environment(\.managedObjectContext) private var viewContext
    @State private var showToolSelection = false
    @State private var demoMember: Member?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                // 标题区域
                VStack(spacing: 12) {
                    Text("🎰 抽奖道具配置演示")
                        .font(.system(size: 28, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text("体验大转盘、盲盒、刮刮卡的配置界面")
                        .font(.system(size: 16))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 60)
                
                // 演示成员卡片
                if let member = demoMember {
                    DemoMemberCard(member: member)
                }
                
                // 功能介绍
                VStack(spacing: 20) {
                    FeatureCard(
                        icon: "circle.grid.cross.fill",
                        title: "大转盘配置",
                        description: "设置4-12个分区，每个分区可配置不同奖品",
                        color: "#FF9500"
                    )
                    
                    FeatureCard(
                        icon: "shippingbox.fill",
                        title: "盲盒配置",
                        description: "设置2-20个盲盒，每个盲盒包含神秘奖品",
                        color: "#007AFF"
                    )
                    
                    FeatureCard(
                        icon: "rectangle.fill",
                        title: "刮刮卡配置",
                        description: "设置2-20张刮刮卡，刮开揭晓奖品",
                        color: "#FF2D92"
                    )
                }
                .padding(.horizontal, 20)
                
                Spacer()
                
                // 开始体验按钮
                Button(action: {
                    showToolSelection = true
                }) {
                    HStack(spacing: 12) {
                        Image(systemName: "play.circle.fill")
                            .font(.system(size: 20))
                        
                        Text("开始体验配置")
                            .font(.system(size: 18, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        LinearGradient(
                            colors: [Color(hex: "#a9d051"), Color(hex: "#8bb83f")],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .cornerRadius(16)
                    .shadow(color: Color(hex: "#a9d051").opacity(0.3), radius: 8, x: 0, y: 4)
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 40)
            }
            .background(
                LinearGradient(
                    colors: [
                        DesignSystem.Colors.background,
                        Color(hex: "#f0f8e8")
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .navigationBarHidden(true)
        }
        .onAppear {
            createDemoMember()
        }
        .overlay(
            Group {
                if showToolSelection, let member = demoMember {
                    LotteryToolSelectionPopupView(
                        isPresented: $showToolSelection,
                        selectedMember: member,
                        onToolSelected: { member, toolType in
                            print("✅ 配置完成: \(member.name ?? "Unknown") - \(toolType.displayName)")
                        }
                    )
                }
            }
        )
    }
    
    private func createDemoMember() {
        let member = Member(context: viewContext)
        member.id = UUID()
        member.name = "小明"
        member.memberNumber = 1
        member.currentPoints = 150
        member.createdAt = Date()
        member.updatedAt = Date()
        demoMember = member
    }
}

/**
 * 演示成员卡片
 */
struct DemoMemberCard: View {
    let member: Member
    
    var body: some View {
        HStack(spacing: 16) {
            // 成员头像
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                DesignSystem.Colors.primary.opacity(0.8),
                                DesignSystem.Colors.primary
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 60, height: 60)
                
                Text(String(member.name?.prefix(1) ?? "?"))
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)
            }
            
            // 成员信息
            VStack(alignment: .leading, spacing: 6) {
                Text(member.name ?? "未知成员")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("编号: \(String(format: "%03d", member.memberNumber))")
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textSecondary)

                HStack(spacing: 4) {
                    Image(systemName: "star.fill")
                        .font(.system(size: 12))
                        .foregroundColor(Color(hex: "#FFD700"))

                    Text("当前积分: \(member.currentPoints)")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textScore)
                }
            }
            
            Spacer()
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.08), radius: 12, x: 0, y: 4)
        .padding(.horizontal, 20)
    }
}

/**
 * 功能介绍卡片
 */
struct FeatureCard: View {
    let icon: String
    let title: String
    let description: String
    let color: String
    
    var body: some View {
        HStack(spacing: 16) {
            // 图标
            ZStack {
                Circle()
                    .fill(Color(hex: color).opacity(0.1))
                    .frame(width: 50, height: 50)
                
                Image(systemName: icon)
                    .font(.system(size: 22, weight: .medium))
                    .foregroundColor(Color(hex: color))
            }
            
            // 内容
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text(description)
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .lineLimit(2)
            }
            
            Spacer()
        }
        .padding(16)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

#Preview {
    LotteryConfigDemoView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
