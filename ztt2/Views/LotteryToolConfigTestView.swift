//
//  LotteryToolConfigTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI
import CoreData

/**
 * 抽奖道具配置测试视图
 * 用于测试和展示抽奖道具配置界面的功能
 */
struct LotteryToolConfigTestView: View {
    
    @Environment(\.managedObjectContext) private var viewContext
    @State private var showToolSelection = false
    @State private var showConfigPopup = false
    @State private var selectedMember: Member?
    @State private var selectedToolType: LotteryConfig.ToolType = .wheel
    @State private var testMembers: [Member] = []
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // 标题
                VStack(spacing: 8) {
                    Text("抽奖道具配置测试")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text("测试大转盘、盲盒、刮刮卡配置界面")
                        .font(.system(size: 16))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .padding(.top, 40)
                
                // 测试成员列表
                if testMembers.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "person.3.fill")
                            .font(.system(size: 48))
                            .foregroundColor(DesignSystem.Colors.textTertiary)
                        
                        Text("暂无测试成员")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        
                        Button(action: createTestMembers) {
                            Text("创建测试成员")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .frame(height: 48)
                                .background(
                                    LinearGradient(
                                        colors: [Color(hex: "#a9d051"), Color(hex: "#8bb83f")],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .cornerRadius(12)
                        }
                        .padding(.horizontal, 40)
                    }
                } else {
                    // 成员选择区域
                    VStack(spacing: 16) {
                        Text("选择成员进行配置")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        LazyVGrid(columns: [
                            GridItem(.flexible()),
                            GridItem(.flexible())
                        ], spacing: 16) {
                            ForEach(testMembers, id: \.objectID) { member in
                                MemberConfigCard(
                                    member: member,
                                    onTap: {
                                        selectedMember = member
                                        showToolSelection = true
                                    }
                                )
                            }
                        }
                        .padding(.horizontal, 20)
                    }
                }
                
                Spacer()
                
                // 直接测试按钮区域
                VStack(spacing: 16) {
                    Text("直接测试配置界面")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    HStack(spacing: 12) {
                        // 大转盘测试按钮
                        ToolTestButton(
                            toolType: .wheel,
                            onTap: {
                                selectedToolType = .wheel
                                selectedMember = testMembers.first ?? createDemoMember()
                                showConfigPopup = true
                            }
                        )
                        
                        // 盲盒测试按钮
                        ToolTestButton(
                            toolType: .blindbox,
                            onTap: {
                                selectedToolType = .blindbox
                                selectedMember = testMembers.first ?? createDemoMember()
                                showConfigPopup = true
                            }
                        )
                        
                        // 刮刮卡测试按钮
                        ToolTestButton(
                            toolType: .scratchcard,
                            onTap: {
                                selectedToolType = .scratchcard
                                selectedMember = testMembers.first ?? createDemoMember()
                                showConfigPopup = true
                            }
                        )
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 40)
            }
            .background(DesignSystem.Colors.background)
            .navigationBarHidden(true)
        }
        .onAppear {
            loadTestMembers()
        }
        .overlay(
            Group {
                if showToolSelection, let member = selectedMember {
                    LotteryToolSelectionPopupView(
                        isPresented: $showToolSelection,
                        selectedMember: member,
                        onToolSelected: { member, toolType in
                            print("选择了道具: \(toolType.displayName) for \(member.name ?? "Unknown")")
                        }
                    )
                } else if showConfigPopup, let member = selectedMember {
                    LotteryToolConfigPopupView(
                        isPresented: $showConfigPopup,
                        selectedMember: member,
                        toolType: selectedToolType,
                        onSave: { formData in
                            let configData = formData.toConfigData()
                            print("保存配置:")
                            print("- 道具类型: \(configData.toolType)")
                            print("- 道具数量: \(configData.itemCount)")
                            print("- 消耗积分: \(configData.costPerPlay)")
                            print("- 奖品列表: \(configData.prizeNames)")
                        },
                        onCancel: {
                            print("取消配置")
                        }
                    )
                }
            }
        )
    }
    
    // MARK: - 私有方法
    
    private func loadTestMembers() {
        let request: NSFetchRequest<Member> = Member.fetchRequest()
        request.predicate = NSPredicate(format: "name BEGINSWITH %@", "测试")
        
        do {
            testMembers = try viewContext.fetch(request)
        } catch {
            print("加载测试成员失败: \(error)")
        }
    }
    
    private func createTestMembers() {
        let memberNames = ["测试小明", "测试小红", "测试小刚", "测试小美"]
        
        for (index, name) in memberNames.enumerated() {
            let member = Member(context: viewContext)
            member.id = UUID()
            member.name = name
            member.memberNumber = Int32(index + 1)
            member.currentPoints = Int32.random(in: 50...200)
            member.createdAt = Date()
            member.updatedAt = Date()
        }
        
        do {
            try viewContext.save()
            loadTestMembers()
        } catch {
            print("创建测试成员失败: \(error)")
        }
    }
    
    private func createDemoMember() -> Member {
        let member = Member(context: viewContext)
        member.id = UUID()
        member.name = "演示成员"
        member.memberNumber = 999
        member.currentPoints = 100
        member.createdAt = Date()
        member.updatedAt = Date()
        return member
    }
}

/**
 * 成员配置卡片
 */
struct MemberConfigCard: View {
    let member: Member
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // 成员头像
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    DesignSystem.Colors.primary.opacity(0.8),
                                    DesignSystem.Colors.primary
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 50, height: 50)
                    
                    Text(String(member.name?.prefix(1) ?? "?"))
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                }
                
                // 成员信息
                VStack(spacing: 4) {
                    Text(member.name ?? "未知")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text("积分: \(member.currentPoints)")
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(16)
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/**
 * 道具测试按钮
 */
struct ToolTestButton: View {
    let toolType: LotteryConfig.ToolType
    let onTap: () -> Void
    
    private var toolColor: String {
        switch toolType {
        case .wheel: return "#FF9500"
        case .blindbox: return "#007AFF"
        case .scratchcard: return "#FF2D92"
        }
    }
    
    private var toolIcon: String {
        switch toolType {
        case .wheel: return "circle.grid.cross.fill"
        case .blindbox: return "shippingbox.fill"
        case .scratchcard: return "rectangle.fill"
        }
    }
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Image(systemName: toolIcon)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                
                Text(toolType.displayName)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.white)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 80)
            .background(
                LinearGradient(
                    colors: [
                        Color(hex: toolColor).opacity(0.8),
                        Color(hex: toolColor)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    LotteryToolConfigTestView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
