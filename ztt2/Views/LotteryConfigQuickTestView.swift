//
//  LotteryConfigQuickTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI
import CoreData

/**
 * 抽奖配置快速测试视图
 * 用于快速测试抽奖道具配置界面功能
 */
struct LotteryConfigQuickTestView: View {
    
    @Environment(\.managedObjectContext) private var viewContext
    @State private var showWheelConfig = false
    @State private var showBlindboxConfig = false
    @State private var showScratchcardConfig = false
    @State private var testMember: Member?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                // 标题
                VStack(spacing: 8) {
                    Text("🎰 抽奖配置快速测试")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text("点击按钮测试各种抽奖道具配置界面")
                        .font(.system(size: 16))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 60)
                
                // 测试成员信息
                if let member = testMember {
                    VStack(spacing: 12) {
                        Text("测试成员")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        HStack(spacing: 12) {
                            ZStack {
                                Circle()
                                    .fill(DesignSystem.Colors.primary)
                                    .frame(width: 40, height: 40)
                                
                                Text(String(member.name?.prefix(1) ?? "T"))
                                    .font(.system(size: 18, weight: .bold))
                                    .foregroundColor(.white)
                            }
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text(member.name ?? "测试成员")
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(DesignSystem.Colors.textPrimary)
                                
                                Text("积分: \(member.currentPoints)")
                                    .font(.system(size: 14))
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                            }
                            
                            Spacer()
                        }
                        .padding(16)
                        .background(Color.white)
                        .cornerRadius(12)
                        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
                    }
                    .padding(.horizontal, 20)
                }
                
                // 测试按钮区域
                VStack(spacing: 20) {
                    Text("选择要测试的抽奖道具")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    VStack(spacing: 16) {
                        // 大转盘测试按钮
                        TestButton(
                            title: "大转盘配置",
                            subtitle: "4-12个分区，每个分区设置奖品",
                            icon: "circle.grid.cross.fill",
                            color: "#FF9500",
                            action: {
                                showWheelConfig = true
                            }
                        )
                        
                        // 盲盒测试按钮
                        TestButton(
                            title: "盲盒配置",
                            subtitle: "2-20个盲盒，每个盲盒设置奖品",
                            icon: "shippingbox.fill",
                            color: "#007AFF",
                            action: {
                                showBlindboxConfig = true
                            }
                        )
                        
                        // 刮刮卡测试按钮
                        TestButton(
                            title: "刮刮卡配置",
                            subtitle: "2-20张刮刮卡，每张设置奖品",
                            icon: "rectangle.fill",
                            color: "#FF2D92",
                            action: {
                                showScratchcardConfig = true
                            }
                        )
                    }
                }
                .padding(.horizontal, 20)
                
                Spacer()
            }
            .background(DesignSystem.Colors.background)
            .navigationBarHidden(true)
        }
        .onAppear {
            createTestMember()
        }
        .overlay(
            Group {
                if showWheelConfig, let member = testMember {
                    LotteryToolConfigPopupView(
                        isPresented: $showWheelConfig,
                        selectedMember: member,
                        toolType: .wheel,
                        onSave: { formData in
                            let config = formData.toConfigData()
                            print("✅ 大转盘配置保存成功:")
                            print("- 分区数量: \(config.itemCount)")
                            print("- 消耗积分: \(config.costPerPlay)")
                            print("- 奖品列表: \(config.prizeNames)")
                        },
                        onCancel: {
                            print("❌ 取消大转盘配置")
                        }
                    )
                } else if showBlindboxConfig, let member = testMember {
                    LotteryToolConfigPopupView(
                        isPresented: $showBlindboxConfig,
                        selectedMember: member,
                        toolType: .blindbox,
                        onSave: { formData in
                            let config = formData.toConfigData()
                            print("✅ 盲盒配置保存成功:")
                            print("- 盲盒数量: \(config.itemCount)")
                            print("- 消耗积分: \(config.costPerPlay)")
                            print("- 奖品列表: \(config.prizeNames)")
                        },
                        onCancel: {
                            print("❌ 取消盲盒配置")
                        }
                    )
                } else if showScratchcardConfig, let member = testMember {
                    LotteryToolConfigPopupView(
                        isPresented: $showScratchcardConfig,
                        selectedMember: member,
                        toolType: .scratchcard,
                        onSave: { formData in
                            let config = formData.toConfigData()
                            print("✅ 刮刮卡配置保存成功:")
                            print("- 刮刮卡数量: \(config.itemCount)")
                            print("- 消耗积分: \(config.costPerPlay)")
                            print("- 奖品列表: \(config.prizeNames)")
                        },
                        onCancel: {
                            print("❌ 取消刮刮卡配置")
                        }
                    )
                }
            }
        )
    }
    
    private func createTestMember() {
        let member = Member(context: viewContext)
        member.id = UUID()
        member.name = "小明"
        member.memberNumber = 1
        member.currentPoints = 120
        member.role = "son"
        member.createdAt = Date()
        member.updatedAt = Date()
        testMember = member
    }
}

/**
 * 测试按钮组件
 */
struct TestButton: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // 图标
                ZStack {
                    Circle()
                        .fill(Color(hex: color).opacity(0.1))
                        .frame(width: 50, height: 50)
                    
                    Image(systemName: icon)
                        .font(.system(size: 22, weight: .medium))
                        .foregroundColor(Color(hex: color))
                }
                
                // 内容
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text(subtitle)
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .lineLimit(2)
                }
                
                Spacer()
                
                // 箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textTertiary)
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    LotteryConfigQuickTestView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
