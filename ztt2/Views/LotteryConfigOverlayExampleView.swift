//
//  LotteryConfigOverlayExampleView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI
import CoreData

/**
 * 抽奖配置Overlay使用示例
 * 展示如何正确使用overlay方式显示配置弹窗
 */
struct LotteryConfigOverlayExampleView: View {
    
    @Environment(\.managedObjectContext) private var viewContext
    @State private var showLotteryConfig = false
    @State private var testMember: Member?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 40) {
                // 标题
                VStack(spacing: 12) {
                    Text("🎰 Overlay弹窗示例")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text("演示正确的overlay弹窗使用方法")
                        .font(.system(size: 16))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 80)
                
                // 成员信息
                if let member = testMember {
                    VStack(spacing: 16) {
                        Text("测试成员")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        HStack(spacing: 16) {
                            ZStack {
                                Circle()
                                    .fill(DesignSystem.Colors.primary)
                                    .frame(width: 60, height: 60)
                                
                                Text(String(member.name?.prefix(1) ?? "T"))
                                    .font(.system(size: 24, weight: .bold))
                                    .foregroundColor(.white)
                            }
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text(member.name ?? "测试成员")
                                    .font(.system(size: 18, weight: .semibold))
                                    .foregroundColor(DesignSystem.Colors.textPrimary)
                                
                                Text("积分: \(member.currentPoints)")
                                    .font(.system(size: 16))
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                            }
                            
                            Spacer()
                        }
                        .padding(20)
                        .background(Color.white)
                        .cornerRadius(16)
                        .shadow(color: Color.black.opacity(0.08), radius: 12, x: 0, y: 4)
                    }
                    .padding(.horizontal, 20)
                }
                
                // 说明文本
                VStack(spacing: 12) {
                    Text("✅ 正确做法")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(Color(hex: "#26C34B"))
                    
                    Text("使用 .overlay() 修饰符显示配置弹窗\n弹窗会覆盖在当前视图上方")
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .lineSpacing(4)
                }
                .padding(.horizontal, 20)
                
                // 触发按钮
                Button(action: {
                    showLotteryConfig = true
                }) {
                    HStack(spacing: 12) {
                        Image(systemName: "gear.circle.fill")
                            .font(.system(size: 20))
                        
                        Text("打开抽奖配置")
                            .font(.system(size: 18, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        LinearGradient(
                            colors: [Color(hex: "#a9d051"), Color(hex: "#8bb83f")],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .cornerRadius(16)
                    .shadow(color: Color(hex: "#a9d051").opacity(0.3), radius: 8, x: 0, y: 4)
                }
                .padding(.horizontal, 20)
                
                Spacer()
                
                // 代码示例
                VStack(alignment: .leading, spacing: 8) {
                    Text("代码示例:")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Text("""
.overlay(
    Group {
        if showConfig, let member = member {
            LotteryToolSelectionPopupView(
                isPresented: $showConfig,
                selectedMember: member,
                onToolSelected: { _, _ in }
            )
        }
    }
)
""")
                        .font(.system(size: 12, family: .monospaced))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .padding(12)
                        .background(Color(hex: "#f8f9fa"))
                        .cornerRadius(8)
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 40)
            }
            .background(DesignSystem.Colors.background)
            .navigationBarHidden(true)
        }
        .onAppear {
            createTestMember()
        }
        // ✅ 正确的overlay用法
        .overlay(
            Group {
                if showLotteryConfig, let member = testMember {
                    LotteryToolSelectionPopupView(
                        isPresented: $showLotteryConfig,
                        selectedMember: member,
                        onToolSelected: { member, toolType in
                            print("✅ 选择了道具: \(toolType.displayName) for \(member.name ?? "Unknown")")
                        }
                    )
                }
            }
        )
    }
    
    private func createTestMember() {
        let member = Member(context: viewContext)
        member.id = UUID()
        member.name = "小明"
        member.memberNumber = 1
        member.currentPoints = 120
        member.role = "son"
        member.createdAt = Date()
        member.updatedAt = Date()
        testMember = member
    }
}

#Preview {
    LotteryConfigOverlayExampleView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
