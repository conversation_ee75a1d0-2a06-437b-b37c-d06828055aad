//
//  LotteryConfigOverlayExampleView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI
import CoreData

/**
 * 抽奖配置Overlay使用示例
 * 展示如何正确使用overlay方式显示配置弹窗
 */
struct LotteryConfigOverlayExampleView: View {
    
    @Environment(\.managedObjectContext) private var viewContext
    @State private var showLotteryConfig = false
    @State private var testMember: Member?
    
    var body: some View {
        NavigationView {
            mainContent
        }
        .onAppear {
            createTestMember()
        }
        // ✅ 正确的overlay用法
        .overlay(overlayContent)
    }

    // MARK: - 视图组件

    private var mainContent: some View {
        VStack(spacing: 40) {
            titleSection
            memberInfoSection
            instructionSection
            triggerButton
            Spacer()
            codeExampleSection
        }
        .background(DesignSystem.Colors.background)
        .navigationBarHidden(true)
    }

    private var titleSection: some View {
        VStack(spacing: 12) {
            Text("🎰 Overlay弹窗示例")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Text("演示正确的overlay弹窗使用方法")
                .font(.system(size: 16))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(.top, 80)
    }
                
    private var memberInfoSection: some View {
        Group {
            if let member = testMember {
                VStack(spacing: 16) {
                    Text("测试成员")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)

                    memberCard(member: member)
                }
                .padding(.horizontal, 20)
            }
        }
    }

    private func memberCard(member: Member) -> some View {
        HStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(DesignSystem.Colors.primary)
                    .frame(width: 60, height: 60)

                Text(String(member.name?.prefix(1) ?? "T"))
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(member.name ?? "测试成员")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text("积分: \(member.currentPoints)")
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }

            Spacer()
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.08), radius: 12, x: 0, y: 4)
    }
                
    private var instructionSection: some View {
        VStack(spacing: 12) {
            Text("✅ 正确做法")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(Color(hex: "#26C34B"))

            Text("使用 .overlay() 修饰符显示配置弹窗\n弹窗会覆盖在当前视图上方")
                .font(.system(size: 14))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .lineSpacing(4)
        }
        .padding(.horizontal, 20)
    }
                
    private var triggerButton: some View {
        Button(action: {
            showLotteryConfig = true
        }) {
            HStack(spacing: 12) {
                Image(systemName: "gear.circle.fill")
                    .font(.system(size: 20))

                Text("打开抽奖配置")
                    .font(.system(size: 18, weight: .semibold))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .background(buttonGradient)
            .cornerRadius(16)
            .shadow(color: Color(hex: "#a9d051").opacity(0.3), radius: 8, x: 0, y: 4)
        }
        .padding(.horizontal, 20)
    }

    private var buttonGradient: LinearGradient {
        LinearGradient(
            colors: [Color(hex: "#a9d051"), Color(hex: "#8bb83f")],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
                
    private var codeExampleSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("代码示例:")
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Text(codeExampleText)
                .font(.system(size: 12, family: .monospaced))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .padding(12)
                .background(Color(hex: "#f8f9fa"))
                .cornerRadius(8)
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 40)
    }

    private var codeExampleText: String {
        """
.overlay(
    Group {
        if showConfig, let member = member {
            LotteryToolSelectionPopupView(
                isPresented: $showConfig,
                selectedMember: member,
                onToolSelected: { _, _ in }
            )
        }
    }
)
"""
    }

    private var overlayContent: some View {
        Group {
            if showLotteryConfig, let member = testMember {
                LotteryToolSelectionPopupView(
                    isPresented: $showLotteryConfig,
                    selectedMember: member,
                    onToolSelected: { member, toolType in
                        print("✅ 选择了道具: \(toolType.displayName) for \(member.name ?? "Unknown")")
                    }
                )
            }
        }
    }

    // MARK: - 私有方法

    private func createTestMember() {
        let member = Member(context: viewContext)
        member.id = UUID()
        member.name = "小明"
        member.memberNumber = 1
        member.currentPoints = 120
        member.role = "son"
        member.createdAt = Date()
        member.updatedAt = Date()
        testMember = member
    }
}

#Preview {
    LotteryConfigOverlayExampleView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
