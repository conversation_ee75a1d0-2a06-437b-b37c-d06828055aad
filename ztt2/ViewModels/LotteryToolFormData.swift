//
//  LotteryToolFormData.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI
import CoreData

/**
 * 抽奖道具表单数据模型
 * 用于管理抽奖道具配置的表单数据和验证逻辑
 */
class LotteryToolFormData: ObservableObject {
    
    // MARK: - 基本配置
    
    /// 道具类型
    @Published var toolType: LotteryConfig.ToolType = .wheel
    
    /// 道具数量（盲盒和刮刮卡可调整，大转盘固定范围4-12）
    @Published var itemCount: Int = 8
    
    /// 每次抽奖消耗积分
    @Published var costPerPlay: Int = 10
    
    // MARK: - 道具项目配置
    
    /// 道具项目数据
    @Published var items: [LotteryToolItemData] = []
    
    // MARK: - 验证状态
    
    /// 表单验证错误
    @Published var validationErrors: [String] = []
    
    /// 是否正在保存
    @Published var isSaving: Bool = false
    
    // MARK: - 计算属性
    
    /**
     * 检查表单是否有效
     */
    var isValid: Bool {
        validate()
        return validationErrors.isEmpty
    }
    
    /**
     * 获取最小道具数量
     */
    var minItemCount: Int {
        return toolType.minItemCount
    }
    
    /**
     * 获取最大道具数量
     */
    var maxItemCount: Int {
        return toolType.maxItemCount
    }
    
    /**
     * 获取本地化的道具标题前缀
     */
    var localizedItemTitlePrefix: String {
        return toolType.itemNamePrefix
    }
    
    // MARK: - 初始化
    
    /**
     * 默认初始化
     */
    init() {
        setupDefaultItems()
    }
    
    /**
     * 使用指定道具类型初始化
     */
    init(toolType: LotteryConfig.ToolType) {
        self.toolType = toolType
        self.itemCount = toolType.defaultItemCount
        setupDefaultItems()
    }
    
    /**
     * 使用现有配置初始化
     */
    init(from config: LotteryConfig) {
        self.toolType = config.lotteryToolType
        self.itemCount = Int(config.itemCount)
        self.costPerPlay = Int(config.costPerPlay)
        
        // 加载现有项目数据
        loadItemsFromConfig(config)
    }
    
    // MARK: - 数据管理
    
    /**
     * 设置道具类型
     */
    func setToolType(_ type: LotteryConfig.ToolType) {
        self.toolType = type
        
        // 重置道具数量为默认值
        self.itemCount = type.defaultItemCount
        
        // 重新设置道具项目
        setupDefaultItems()
    }
    
    /**
     * 从现有配置加载数据
     */
    func loadFromExistingConfig(_ config: LotteryConfig) {
        self.toolType = config.lotteryToolType
        self.itemCount = Int(config.itemCount)
        self.costPerPlay = Int(config.costPerPlay)
        
        // 加载现有项目数据
        loadItemsFromConfig(config)
    }
    
    /**
     * 设置道具数量
     */
    func setItemCount(_ count: Int) {
        let clampedCount = max(minItemCount, min(maxItemCount, count))
        self.itemCount = clampedCount
        
        // 调整项目数组
        adjustItemsArray()
    }
    
    /**
     * 更新指定索引的奖品名称
     */
    func updateItemPrizeName(at index: Int, name: String) {
        guard index >= 0 && index < items.count else { return }
        items[index].prizeName = name
    }
    
    // MARK: - 私有方法
    
    /**
     * 设置默认项目
     */
    private func setupDefaultItems() {
        items.removeAll()
        
        for i in 0..<itemCount {
            let item = LotteryToolItemData(
                index: i + 1,
                prizeName: "",
                toolType: toolType
            )
            items.append(item)
        }
    }
    
    /**
     * 从配置加载项目数据
     */
    private func loadItemsFromConfig(_ config: LotteryConfig) {
        items.removeAll()
        
        let sortedItems = config.allItems
        
        for (index, item) in sortedItems.enumerated() {
            let itemData = LotteryToolItemData(
                index: index + 1,
                prizeName: item.prizeName ?? "",
                toolType: toolType
            )
            items.append(itemData)
        }
        
        // 如果项目数量不足，补充空项目
        while items.count < itemCount {
            let item = LotteryToolItemData(
                index: items.count + 1,
                prizeName: "",
                toolType: toolType
            )
            items.append(item)
        }
    }
    
    /**
     * 调整项目数组大小
     */
    private func adjustItemsArray() {
        if items.count < itemCount {
            // 添加新项目
            for i in items.count..<itemCount {
                let item = LotteryToolItemData(
                    index: i + 1,
                    prizeName: "",
                    toolType: toolType
                )
                items.append(item)
            }
        } else if items.count > itemCount {
            // 移除多余项目
            items = Array(items.prefix(itemCount))
        }
        
        // 更新索引
        for (index, item) in items.enumerated() {
            item.index = index + 1
        }
    }
    
    /**
     * 验证表单数据
     */
    private func validate() {
        validationErrors.removeAll()
        
        // 验证积分消耗
        if costPerPlay <= 0 {
            validationErrors.append("lottery_config.validation.cost_required".localized)
        }

        // 验证道具数量
        if itemCount < minItemCount || itemCount > maxItemCount {
            validationErrors.append("lottery_config.validation.item_count_range".localized(with: minItemCount, maxItemCount))
        }

        // 验证奖品名称
        let emptyCount = items.filter { $0.prizeName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }.count
        let duplicateNames = Set(items.map { $0.prizeName.trimmingCharacters(in: .whitespacesAndNewlines) }.filter { !$0.isEmpty })

        // 添加验证错误
        if emptyCount > 0 {
            validationErrors.append("lottery_config.validation.empty_prize_names".localized(with: emptyCount, localizedItemTitlePrefix))
        }

        if duplicateNames.count != items.filter({ !$0.prizeName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }).count {
            validationErrors.append("lottery_config.validation.duplicate_prize_names".localized)
        }
    }
    
    // MARK: - 数据导出
    
    /**
     * 导出为配置数据
     */
    func toConfigData() -> (toolType: String, itemCount: Int, costPerPlay: Int, prizeNames: [String]) {
        let prizeNames = items.map { $0.prizeName.trimmingCharacters(in: .whitespacesAndNewlines) }
        return (
            toolType: toolType.rawValue,
            itemCount: itemCount,
            costPerPlay: costPerPlay,
            prizeNames: prizeNames
        )
    }
    
    /**
     * 重置表单
     */
    func reset() {
        toolType = .wheel
        itemCount = 8
        costPerPlay = 10
        validationErrors.removeAll()
        isSaving = false
        setupDefaultItems()
    }
}

/**
 * 抽奖道具项目数据
 */
class LotteryToolItemData: ObservableObject, Identifiable {
    let id = UUID()
    
    /// 项目索引
    @Published var index: Int
    
    /// 奖品名称
    @Published var prizeName: String
    
    /// 道具类型
    let toolType: LotteryConfig.ToolType
    
    /// 显示标题
    var displayTitle: String {
        switch toolType {
        case .wheel:
            return "lottery_config.item.wheel_title".localized(with: index)
        case .blindbox:
            return "lottery_config.item.blindbox_title".localized(with: index)
        case .scratchcard:
            return "lottery_config.item.scratchcard_title".localized(with: index)
        }
    }

    /// 占位符文本
    var placeholderText: String {
        switch toolType {
        case .wheel:
            return "lottery_config.item.wheel_placeholder".localized(with: index)
        case .blindbox:
            return "lottery_config.item.blindbox_placeholder".localized(with: index)
        case .scratchcard:
            return "lottery_config.item.scratchcard_placeholder".localized(with: index)
        }
    }
    
    init(index: Int, prizeName: String, toolType: LotteryConfig.ToolType) {
        self.index = index
        self.prizeName = prizeName
        self.toolType = toolType
    }
}
